# PSModel - 电力系统建模工具

本应用是一个基于Tauri、Vue 3和AntV X6构建的电力系统建模和仿真桌面应用。它提供了一个可视化的编辑器，用于创建、自定义和仿真电力系统图。同时，项目后端也可作为独立的Web服务运行。

## ✨ 功能特性

*   **可视化编辑器**: 使用拖放方式构建复杂的电力系统图。
*   **组件自定义**: 支持自定义组件的外观和属性。
*   **仿真视图**: 对创建的电路图进行仿真。
*   **Web服务模式**: 后端可独立部署，通过HTTP API提供服务。
*   **跨平台**: 基于Tauri构建，可打包为Windows、macOS和Linux应用。

## 🛠️ 技术栈

*   **前端**:
    *   [Vue 3](https://vuejs.org/)
    *   [Vite](https://vitejs.dev/)
    *   [TypeScript](https://www.typescriptlang.org/)
    *   [AntV X6](https://x6.antv.vision/): 用于图编辑的核心库。
    *   [Element Plus](https://element-plus.org/): UI组件库。
    *   [Pinia](https://pinia.vuejs.org/): 状态管理。
    *   [Vue Router](https://router.vuejs.org/): 路由管理。
*   **后端 & 打包**:
    *   [Tauri](https://tauri.app/): 使用Web技术构建桌面应用的核心框架。
    *   [Rust](https://www.rust-lang.org/): Tauri后端及Web服务的主要语言。
    *   [Axum](https://github.com/tokio-rs/axum): 高性能的Rust Web框架。
    *   [Tokio](https://tokio.rs/): Rust的异步运行时。
    *   [LMDB](https://symas.com/lmdb/): 高性能的嵌入式键值数据库。
    *   [Tracing](https://github.com/tokio-rs/tracing): Rust的应用级日志框架。

## 🚀 快速开始

### 先决条件

在开始之前，请确保您已经安装了 [Node.js](https://nodejs.org/) (推荐LTS版本) 和 [Rust](https://www.rust-lang.org/tools/install)。

同时，您需要根据Tauri官方文档 [设置您的开发环境](https://tauri.app/v1/guides/getting-started/prerequisites)。

### 安装

1.  **克隆仓库**
    ```bash
    git clone <your-repository-url>
    cd psmodel-web
    ```

2.  **安装依赖**
    ```bash
    npm install
    ```

### 运行开发环境

本项目支持两种开发模式：

1.  **Tauri 桌面应用模式**
    此命令会同时启动Vite前端开发服务器和Tauri应用。
    ```bash
    npm run tauri dev
    ```

2.  **纯Web服务模式**
    此命令会同时启动Vite前端开发服务器和后端的Axum Web服务。适用于纯Web开发或后端API调试。
    ```bash
    npm run dev:web
    ```

### 可用脚本

*   `npm run dev`: 仅启动Vite前端开发服务器。
*   `npm run build`: 编译TypeScript并使用Vite构建前端代码。
*   `npm run preview`: 在本地预览构建后的应用。
*   `npm run tauri`: Tauri CLI的快捷方式，可用于 `tauri dev` 和 `tauri build` 等命令。
*   `npm run serve:web`: 仅启动后端的Axum Web服务。
*   `npm run dev:web`: 同时启动前端和后端Web服务进行开发。

### 构建应用

*   **构建桌面应用**:
    ```bash
    npm run tauri build
    ```
    构建产物将位于 `src-tauri/target/release/` 目录下。

*   **构建Web服务**:
    ```bash
    cargo build --release --bin web --manifest-path src-tauri/Cargo.toml
    ```
    构建产物将位于 `src-tauri/target/release/` 目录下。

## 📁 项目结构

```
.
├── src/                      # 前端源代码 (Vue, TypeScript)
│   ├── api/                  # API请求封装
│   ├── assets/               # 静态资源 (图片, SVG)
│   ├── layout/               # 应用布局
│   ├── router/               # Vue Router配置
│   ├── store/                # Pinia状态管理
│   ├── utils/                # 工具函数
│   ├── views/                # 页面视图
│   ├── App.vue               # 根Vue组件
│   └── main.ts               # 应用入口
├── src-tauri/                # 后端源代码 (Rust, Tauri)
│   ├── src/
│   │   ├── api/              # 后端API模块
│   │   ├── bin/
│   │   │   └── web.rs        # Axum Web服务入口
│   │   ├── lib.rs            # Tauri库入口
│   │   └── main.rs           # Tauri应用入口
│   └── tauri.conf.json       # Tauri应用配置
├── package.json              # 项目依赖和脚本
└── vite.config.ts            # Vite配置
