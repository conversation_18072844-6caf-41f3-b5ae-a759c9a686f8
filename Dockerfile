# 第一阶段编译前端代码
FROM registry.tode.ltd/node:18.20.8 AS builder1

COPY --chown=root:root .  /app/
WORKDIR /app/
RUN npm i -g pnpm --registry=https://registry.npmmirror.com
RUN pnpm i --registry=https://registry.npmmirror.com
RUN npm run build

# ---- Builder Stage ----
# 使用官方的 Rust 镜像作为构建环境
FROM registry.tode.ltd/rust:1.88


# 设置工作目录
WORKDIR /root/psmodel
# 复制项目源码
COPY src-tauri .

SHELL [ "/bin/bash", "-c" ]
ENV SHELL=/bin/bash
ENV PATH="/root/.cargo/bin:$PATH"
# 利用 Docker build cache 来缓存编译产物和下载的 crates
RUN cargo build --release --bin web
RUN mkdir -p /dist /app
# 拷贝所有代码进入容器
COPY --from=builder1 /app/dist /dist/
RUN cp -f /root/psmodel/target/release/web /app/
COPY start.sh  /app
RUN rm -rf /root/psmodel

# 暴露应用程序端口
EXPOSE 80
WORKDIR /app

# 设置入口点
CMD ["/app/start.sh"]