import { createApp } from "vue";
import { createPinia } from "pinia";
import ElementPlus from "element-plus";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import "element-plus/dist/index.css";
import "element-plus/theme-chalk/display.css";
import App from "./App.vue";
import router from './router';
import { setBaseUrl } from './api/request';
import { invoke } from '@tauri-apps/api/core';

const isTauri = !!(window as any).__TAURI__;

async function initializeApp() {
  const app = createApp(App);  

  // 注册 Element Plus 图标
  for (const [key, component] of Object.entries(ElementPlusIconsVue))
    app.component(key, component);

  // 安装插件
  app.use(ElementPlus);
  app.use(createPinia());
  app.use(router);
  app.mount("#app");

  // 仅在 Tauri 环境下调用命令并设置 baseURL
  if (isTauri) {
    try {
      const port = await invoke<number>('get_backend_port');
      console.log(`Backend is ready on port: ${port}`);
      setBaseUrl(`http://127.0.0.1:${port}`);
    } catch (e) {
      console.error("Failed to get backend port:", e);
    }
  }

 
}

initializeApp();
