import { ref, onMounted, onUnmounted } from 'vue';

export default function useDraw() {
  const appRef = ref<HTMLElement | null>(null);

  const setViewportStyles = () => {
    if (!appRef.value) return;
    
    const container = appRef.value;
    container.style.width = '100vw';
    container.style.height = '100vh';
    container.style.overflow = 'hidden';
    container.style.transformOrigin = 'top left';
    
    const scale = Math.min(
      window.innerWidth / 1920,
      window.innerHeight / 1080
    );
    
    container.style.transform = `scale(${scale})`;
  };

  let resizeObserver: ResizeObserver | null = null;

  onMounted(() => {
    setViewportStyles();
    
    resizeObserver = new ResizeObserver(setViewportStyles);
    if (appRef.value) {
      resizeObserver.observe(appRef.value);
    }

    window.addEventListener('resize', setViewportStyles);
  });

  onUnmounted(() => {
    if (resizeObserver) {
      resizeObserver.disconnect();
      resizeObserver = null;
    }
    window.removeEventListener('resize', setViewportStyles);
  });

  return {
    appRef
  };
}
