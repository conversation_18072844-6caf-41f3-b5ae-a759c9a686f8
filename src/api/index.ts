import request from './request';
import type { ApiResponse, Component } from './types';

export function login(data: any) {
  return request({
    url: '/user/login',
    method: 'post',
    data,
  });
}

export function getComponentTypeList() {
  return request({
    url: '/api/v1/component/type/list',
    method: 'get',
  });
}

export function getComponentList({ component_type_id = 1 }: {
  component_type_id?: number
} = {}): Promise<ApiResponse<Component[]>> {
  return request({
    url: '/api/v1/component/list',
    method: 'get',
    params: {
      component_type_id
    }
  });
}

export function addComponent(data: {
  name: string;
  keyword: string;
  component_type_id: number;
  description?: string;
}) {
  return request({
    url: '/api/v1/component/add',
    method: 'post',
    data,
  });
}

export function updateComponent(data: {
  component_id: string;
  name: string;
  keyword: string;
  component_type_id: number;
  description?: string;
}): Promise<ApiResponse> {
  return request({
    url: '/api/v1/component/update',
    method: 'post',
    data,
  });
}

export function deleteComponent(data: {
  component_id: string;
}): Promise<ApiResponse> {
  return request({
    url: '/api/v1/component/delete',
    method: 'post',
    data,
  });
}

export function getComponent(params: {
  component_id: string;
}): Promise<ApiResponse<Component>> {
  return request({
    url: '/api/v1/component/get',
    method: 'get',
    params,
  });
}

// 保存图数据
export function saveDiagram(data: {
  component_id: string;
  property: object;
  definition: object;
  diagram: object;
}): Promise<ApiResponse> {
  return request({
    url: '/api/v1/component/diagram/save',
    method: 'post',
    data,
  });
}

// 获取图数据
export function getDiagram(params: {
  component_id: string;
}): Promise<ApiResponse<{
  component_id: string;
  create_time: string;
  definition: object;
  [key: string]: any;
}>> {
  return request({
    url: '/api/v1/component/diagram/get',
    method: 'get',
    params,
  });
}

// 新建元件类型
export function addComponentType(data: {
  name: string;
  remark?: string;
}): Promise<ApiResponse> {
  return request({
    url: '/api/v1/component/type/add',
    method: 'post',
    data,
  });
}

// 更新元件类型
export function updateComponentType(data: {
  id: number;
  name: string;
  remark?: string;
}): Promise<ApiResponse> {
  return request({
    url: '/api/v1/component/type/update',
    method: 'post',
    data,
  });
}

// 删除元件类型
export function deleteComponentType(data: {
  id: number;
}): Promise<ApiResponse> {
  return request({
    url: '/api/v1/component/type/delete',
    method: 'post',
    data,
  });
}