import axios, { InternalAxiosRequestConfig, AxiosResponse } from 'axios';
import type { ApiResponse } from './types';

const service = axios.create({
  // baseURL 将在 main.ts 中动态设置
  baseURL: "", 
  timeout: 5000,
});

service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    return config;
  },
  (error: any) => {
    return Promise.reject(error);
  }
);

service.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    console.log('response: AxiosResponse', response)
    const res = response.data;
    if (res.code !== 200) {
      return Promise.reject(new Error(res.message || 'Error'));
    }
    return res as any;
  },
  (error: any) => {
    return Promise.reject(error);
  }
);

// 导出一个函数来设置 baseURL，用于 Tauri 模式下覆盖默认值
export const setBaseUrl = (url: string) => {
  service.defaults.baseURL = url;
};

export default service;