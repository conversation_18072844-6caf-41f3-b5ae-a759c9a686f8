<template>
  <div class="simulation-container">
    <div class="actions">
      <el-button type="primary" @click="openCustomization">新建文件</el-button>
      <el-button>打开文件</el-button>
      <el-button>导入文件</el-button>
    </div>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>资源广场</span>
        </div>
      </template>
      <div class="resource-grid">
        <div v-for="item in resources" :key="item" class="resource-item">
          <div class="resource-icon"></div>
          <span>{{ item }}</span>
        </div>
      </div>
    </el-card>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>最近打开</span>
        </div>
      </template>
      <div v-for="o in 4" :key="o" class="recent-item">
        <div class="recent-info">
          <el-icon><Document /></el-icon>
          <div>
            <p>110kV变电站二次系统图</p>
            <span>标准二/标准三</span>
          </div>
        </div>
        <div class="recent-version">
          <span>V1.0.1</span>
          <span>2025/6/17 12:23:56</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { Document } from '@element-plus/icons-vue';

const router = useRouter();

const resources = ref([
  '旋转电机', 'IEEE', '电压源变换器', '储能并网友好', '光伏并网发电', '模块化多电平', '综合能源', '继电保护'
]);

const openCustomization = () => {
  router.push('/customization');
};
</script>

<style scoped>
.simulation-container {
  padding: 20px;
}
.actions {
  margin-bottom: 20px;
  text-align: right;
}
.box-card {
  margin-bottom: 20px;
}
.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 20px;
}
.resource-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}
.resource-icon {
  width: 80px;
  height: 80px;
  background-color: #f0f2f5;
  border-radius: 8px;
}
.recent-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e6e6e6;
}
.recent-item:last-child {
  border-bottom: none;
}
.recent-info {
  display: flex;
  align-items: center;
  gap: 10px;
}
.recent-info .el-icon {
  font-size: 32px;
}
.recent-version {
  text-align: right;
  color: #999;
}
</style>