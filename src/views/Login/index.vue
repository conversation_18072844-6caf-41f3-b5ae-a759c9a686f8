<template>
  <div class="login-container">
    <div class="login-form-wrapper">
      <h2>系统登录</h2>
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        label-position="left"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            name="username"
            type="text"
            placeholder="请输入用户名"
            :prefix-icon="User"
          />
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            name="password"
            type="password"
            placeholder="请输入密码"
            show-password
            :prefix-icon="Lock"
          />
        </el-form-item>
         <el-form-item>
            <div class="form-options">
              <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
              <el-link type="primary" :underline="false">忘记密码?</el-link>
            </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" native-type="submit" class="login-button" :loading="loading">
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { User, Lock } from '@element-plus/icons-vue';

const emit = defineEmits(['login-success']);
const router = useRouter();

const loginFormRef = ref<FormInstance | null>(null);
const loading = ref(false);

const loginForm = reactive({
  username: '',
  password: '',
  rememberMe: false,
});

const loginRules = reactive<FormRules>({
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
});

const handleLogin = () => {
  if (!loginFormRef.value) return;
  loginFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      // 模拟API调用
      setTimeout(() => {
        loading.value = false;
        ElMessage.success('登录成功！');
        // 保存登录状态
        localStorage.setItem('token', 'mock-token');
        localStorage.setItem('userInfo', JSON.stringify({ username: loginForm.username }));
        emit('login-success');
        // 跳转到首页
        router.push('/home');
      }, 2000);
    } else {
      ElMessage.error('请输入用户名和密码');
    }
  });
};
</script>

<style scoped lang="scss">
.login-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 100vh;
  background-image: url('@/assets/images/login/login-bg.jpeg');
  background-size: cover;
  background-position: center;

  .login-form-wrapper {
    width: 400px;
    margin-left: 15vw;
    padding: 40px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;

    h2 {
      margin-bottom: 30px;
      font-size: 24px;
      color: #333;
    }

    .login-form {
      .el-form-item {
        margin-bottom: 25px;
      }
      
      .el-input {
        height: 40px;
      }

      .form-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
      }

      .login-button {
        width: 100%;
        height: 40px;
      }
    }
  }
}
</style>