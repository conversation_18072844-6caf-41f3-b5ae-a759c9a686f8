<template>
  <div 
    :style="{ width: width + 'px', height: height + 'px' }" 
    class="dynamic-svg-component"
  >
    <div 
      class="svg-wrapper"
      v-html="svgContent"
      @mousedown.stop="handleMouseDown"
      @dragstart.prevent
    ></div>
   
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';

const { node } = defineProps({ node: null });

const width = ref(node?.data?.width || 120);
const height = ref(node?.data?.height || 80);

// 监听节点数据变化
watch(() => node?.data, (newData) => {
  if (newData) {
    width.value = newData.width || width.value;
    height.value = newData.height || height.value;
  }
}, { deep: true });

// 获取动态SVG内容
const svgContent = computed(() => {
  const content = node?.data?.svgData || '<svg><rect width="100%" height="100%" fill="#f0f0f0" stroke="#ccc"/></svg>';
  console.log('DynamicSvg svgContent:', content);
  console.log('DynamicSvg node data:', node?.data);
  console.log('DynamicSvg node shape:', node?.shape);
  return content;
});

// 处理鼠标事件
const handleMouseDown = (event: MouseEvent) => {
  // 阻止事件冒泡，确保拖拽正常工作
  event.stopPropagation();
};

onMounted(() => {
  console.log('DynamicSvg mounted, node:', node?.shape, 'data:', node?.data);
  
  // 监听节点变化
  if (node) {
    node.on('change:data', ({ cell, current }: any) => {
      if (current.width && current.height) {
        cell.size({ width: current.width, height: current.height });
        width.value = current.width;
        height.value = current.height;
      }
    });

    // 确保初始大小正确
    if (node.data) {
      width.value = node.data.width || width.value;
      height.value = node.data.height || height.value;
    }
  }
});
</script>

<style scoped lang="scss">
.dynamic-svg-component {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible; /* 允许SVG内容完全显示 */
  position: relative;
  width: 100%;
  height: 100%;
  background: transparent;
}

.svg-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none; /* 禁用事件，避免干扰拖拽 */
  overflow: visible;
}

/* 最小化SVG样式干扰，让SVG自然渲染 */
:deep(svg) {
  pointer-events: none; /* 禁用SVG事件，避免干扰拖拽 */
  display: block;
  width: 100%;
  height: 100%;
}

/* 确保SVG内容正确显示 */
:deep(svg *) {
  pointer-events: none; /* 禁用SVG子元素事件，避免干扰拖拽 */
}

/* 调试信息样式 */
.debug-info {
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 10px;
  padding: 4px;
  z-index: 1000;
  pointer-events: none;
}
</style>