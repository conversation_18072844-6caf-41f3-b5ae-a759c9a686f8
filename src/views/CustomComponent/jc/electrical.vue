<template>
  <div :style="{ width: width + 'px', height: height + 'px' }" class="electrical-component">
    <!-- 晶闸管 -->
    <svg v-if="componentType === 'thyristor'" :width="width" :height="height" viewBox="0 0 40 60">
      <line x1="20" y1="5" x2="20" y2="15" stroke="currentColor" stroke-width="2" />
      <polygon points="10,15 30,15 20,35" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="35" x2="20" y2="45" stroke="currentColor" stroke-width="2" />
      <line x1="5" y1="25" x2="15" y2="25" stroke="currentColor" stroke-width="2" />
      <circle cx="15" cy="25" r="2" fill="currentColor" />
      <line x1="20" y1="45" x2="20" y2="55" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 二极管 -->
    <svg v-else-if="componentType === 'diode'" :width="width" :height="height" viewBox="0 0 40 60">
      <line x1="20" y1="5" x2="20" y2="15" stroke="currentColor" stroke-width="2" />
      <polygon points="10,15 30,15 20,35" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="10" y1="35" x2="30" y2="35" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="35" x2="20" y2="55" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 三极管 -->
    <svg v-else-if="componentType === 'transistor'" :width="width" :height="height" viewBox="0 0 40 60">
      <g fill="none" stroke="currentColor" stroke-width="2">
        <!-- 基极垂直线 -->
        <line x1="15" y1="15" x2="15" y2="35" />
        <!-- 集电极线 -->
        <line x1="15" y1="20" x2="25" y2="15" />
        <line x1="25" y1="15" x2="25" y2="5" />
        <!-- 发射极线 -->
        <line x1="15" y1="30" x2="25" y2="35" />
        <line x1="25" y1="35" x2="25" y2="45" />
        <!-- 基极连接线 -->
        <line x1="5" y1="25" x2="15" y2="25" />
        <!-- 发射极箭头 -->
        <path d="M22 32 L25 35 L22 38" fill="none" stroke-linecap="round" stroke-linejoin="round" />
      </g>
    </svg>
    
    <!-- 电阻 -->
    <svg v-else-if="componentType === 'resistor'" :width="width" :height="height" viewBox="0 0 60 40">
      <line x1="5" y1="20" x2="15" y2="20" stroke="currentColor" stroke-width="2" />
      <path d="M15,20 L20,10 L25,30 L30,10 L35,30 L40,10 L45,20" fill="none" stroke="currentColor" stroke-width="2" />
      <line x1="45" y1="20" x2="55" y2="20" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 电容 -->
    <svg v-else-if="componentType === 'capacitor'" :width="width" :height="height" viewBox="0 0 40 60">
      <line x1="20" y1="5" x2="20" y2="25" stroke="currentColor" stroke-width="2" />
      <line x1="10" y1="25" x2="30" y2="25" stroke="currentColor" stroke-width="3" />
      <line x1="10" y1="35" x2="30" y2="35" stroke="currentColor" stroke-width="3" />
      <line x1="20" y1="35" x2="20" y2="55" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 电感 -->
    <svg v-else-if="componentType === 'inductor'" :width="width" :height="height" viewBox="0 0 60 40">
      <line x1="5" y1="20" x2="15" y2="20" stroke="currentColor" stroke-width="2" />
      <path d="M15,20 Q20,10 25,20 Q30,10 35,20 Q40,10 45,20" fill="none" stroke="currentColor" stroke-width="2" />
      <line x1="45" y1="20" x2="55" y2="20" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 变压器 -->
    <svg v-else-if="componentType === 'transformer'" :width="width" :height="height" viewBox="0 0 60 60">
      <circle cx="20" cy="30" r="12" fill="none" stroke="currentColor" stroke-width="2" />
      <circle cx="40" cy="30" r="12" fill="none" stroke="currentColor" stroke-width="2" />
      <line x1="32" y1="15" x2="32" y2="45" stroke="currentColor" stroke-width="2" />
      <line x1="28" y1="15" x2="28" y2="45" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="5" x2="20" y2="18" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="42" x2="20" y2="55" stroke="currentColor" stroke-width="2" />
      <line x1="40" y1="5" x2="40" y2="18" stroke="currentColor" stroke-width="2" />
      <line x1="40" y1="42" x2="40" y2="55" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 开关 -->
    <svg v-else-if="componentType === 'switch'" :width="width" :height="height" viewBox="0 0 60 40">
      <line x1="5" y1="20" x2="15" y2="20" stroke="currentColor" stroke-width="2" />
      <circle cx="15" cy="20" r="3" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="18" y1="20" x2="42" y2="10" stroke="currentColor" stroke-width="2" />
      <circle cx="45" cy="20" r="3" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="45" y1="20" x2="55" y2="20" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 接地 -->
    <svg v-else-if="componentType === 'ground'" :width="width" :height="height" viewBox="0 0 40 40">
      <line x1="20" y1="5" x2="20" y2="20" stroke="currentColor" stroke-width="2" />
      <line x1="10" y1="20" x2="30" y2="20" stroke="currentColor" stroke-width="3" />
      <line x1="13" y1="25" x2="27" y2="25" stroke="currentColor" stroke-width="2" />
      <line x1="16" y1="30" x2="24" y2="30" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 电压源 -->
    <svg v-else-if="componentType === 'voltage_source'" :width="width" :height="height" viewBox="0 0 40 40">
      <circle cx="20" cy="20" r="12" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="16" y1="20" x2="24" y2="20" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="16" x2="20" y2="24" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="5" x2="20" y2="8" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="32" x2="20" y2="35" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 电流源 -->
    <svg v-else-if="componentType === 'current_source'" :width="width" :height="height" viewBox="0 0 40 40">
      <circle cx="20" cy="20" r="12" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="12" x2="20" y2="28" stroke="currentColor" stroke-width="2" />
      <polygon points="17,25 20,28 23,25" fill="currentColor" />
      <line x1="20" y1="5" x2="20" y2="8" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="32" x2="20" y2="35" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 电机 -->
    <svg v-else-if="componentType === 'motor'" :width="width" :height="height" viewBox="0 0 50 50">
      <circle cx="25" cy="25" r="18" fill="white" stroke="currentColor" stroke-width="2" />
      <circle cx="25" cy="25" r="12" fill="none" stroke="currentColor" stroke-width="1" />
      <text x="25" y="30" font-size="14" text-anchor="middle" fill="currentColor">M</text>
      <line x1="25" y1="5" x2="25" y2="7" stroke="currentColor" stroke-width="2" />
      <line x1="25" y1="43" x2="25" y2="45" stroke="currentColor" stroke-width="2" />
      <line x1="5" y1="25" x2="7" y2="25" stroke="currentColor" stroke-width="2" />
      <line x1="43" y1="25" x2="45" y2="25" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 发电机 -->
    <svg v-else-if="componentType === 'generator'" :width="width" :height="height" viewBox="0 0 50 50">
      <circle cx="25" cy="25" r="18" fill="white" stroke="currentColor" stroke-width="2" />
      <circle cx="25" cy="25" r="12" fill="none" stroke="currentColor" stroke-width="1" />
      <text x="25" y="30" font-size="14" text-anchor="middle" fill="currentColor">G</text>
      <line x1="25" y1="5" x2="25" y2="7" stroke="currentColor" stroke-width="2" />
      <line x1="25" y1="43" x2="25" y2="45" stroke="currentColor" stroke-width="2" />
      <line x1="5" y1="25" x2="7" y2="25" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 熔断器 -->
    <svg v-else-if="componentType === 'fuse'" :width="width" :height="height" viewBox="0 0 60 40">
      <line x1="5" y1="20" x2="15" y2="20" stroke="currentColor" stroke-width="2" />
      <rect x="15" y="15" width="30" height="10" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="20" x2="40" y2="20" stroke="currentColor" stroke-width="1" />
      <path d="M25,20 L30,15 L35,25 L40,15" fill="none" stroke="currentColor" stroke-width="1" />
      <line x1="45" y1="20" x2="55" y2="20" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 继电器 -->
    <svg v-else-if="componentType === 'relay'" :width="width" :height="height" viewBox="0 0 60 60">
      <rect x="10" y="35" width="20" height="15" fill="white" stroke="currentColor" stroke-width="2" />
      <path d="M15,42 Q20,38 25,42" fill="none" stroke="currentColor" stroke-width="1" />
      <line x1="15" y1="35" x2="15" y2="30" stroke="currentColor" stroke-width="2" />
      <line x1="25" y1="35" x2="25" y2="30" stroke="currentColor" stroke-width="2" />
      <circle cx="40" cy="15" r="3" fill="white" stroke="currentColor" stroke-width="2" />
      <circle cx="50" cy="25" r="3" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="43" y1="15" x2="47" y2="10" stroke="currentColor" stroke-width="2" />
      <line x1="40" y1="5" x2="40" y2="12" stroke="currentColor" stroke-width="2" />
      <line x1="50" y1="28" x2="50" y2="35" stroke="currentColor" stroke-width="2" />
      <line x1="25" y1="42" x2="40" y2="15" stroke="currentColor" stroke-width="1" stroke-dasharray="2,2" />
    </svg>
    
    <!-- 自定义引脚 -->
    <svg v-else-if="componentType === 'custom-pin'" :width="width" :height="height" viewBox="0 0 24 24">
      <!-- 简化的引脚：只有一个圆形，便于精确定位 -->
      <circle cx="12" cy="12" r="6" fill="none" stroke="#5F95FF" stroke-width="2" />
      <circle cx="12" cy="12" r="2" fill="#5F95FF" />
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';

const { node } = defineProps({ node: null });

const width = ref(node?.data?.width || 40);
const height = ref(node?.data?.height || 40);

// 从节点的shape属性获取组件类型
const componentType = computed(() => {
  return node?.shape || 'resistor';
});

onMounted(() => {
  node.on('change:data', ({ cell, current }: any) => {
    cell.size({ width: current.width, height: current.height });
    height.value = current.height;
    width.value = current.width;
  });
});
</script>

<style scoped lang="scss">
.electrical-component {
  display: flex;
  align-items: center;
  justify-content: center;
}

svg {
  color: var(--color-primary, #5F95FF);
}
</style>