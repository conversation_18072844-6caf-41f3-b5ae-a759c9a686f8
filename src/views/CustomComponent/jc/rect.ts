import { Graph, Node } from '@antv/x6';
import { ports } from '../../Customization/config';

// 注册自定义矩形节点
Graph.registerNode(
  'custom-rect',
  {
    inherit: 'rect', // 继承内置的矩形节点
    width: 100,
    height: 60,
    attrs: {
      body: {
        strokeWidth: 2,
        stroke: '#5F95FF',
        fill: '#EFF4FF',
        rx: 4, // 圆角
        ry: 4,
      },
      text: {
        fontSize: 12,
        fill: '#262626',
        textAnchor: 'middle',
        textVerticalAnchor: 'middle',
      },
    },
    ports: {
      ...ports,
    },
  },
  true
);

// 创建矩形节点的工厂函数
export function createRectNode(options: {
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  label?: string;
  id?: string;
}) {
  const {
    x = 100,
    y = 100,
    width = 100,
    height = 60,
    label = '矩形元件',
    id,
  } = options;

  return new Node({
    id,
    shape: 'custom-rect',
    x,
    y,
    width,
    height,
    label,
    attrs: {
      body: {
        strokeWidth: 2,
        stroke: '#5F95FF',
        fill: '#EFF4FF',
      },
      text: {
        text: label,
      },
    },
    ports: {
      ...ports,
    },
  });
}

// 矩形节点类，提供更多自定义功能
export class CustomRectNode extends Node {
  constructor(options: any) {
    super({
      shape: 'custom-rect',
      ...options,
      ports: {
        ...ports,
      },
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    // 鼠标悬停显示连接桩
    this.on('mouseenter', () => {
      this.showPorts();
    });

    // 鼠标离开隐藏连接桩
    this.on('mouseleave', () => {
      this.hidePorts();
    });

    // 监听尺寸变化
    this.on('change:size', ({ }: any) => {
      this.updatePortsPosition();
    });
  }

  // 显示连接桩
  showPorts() {
    const ports = this.getPorts();
    ports.forEach((port) => {
      this.setPortProp(port.id!, 'attrs/circle/style/visibility', 'visible');
    });
  }

  // 隐藏连接桩
  hidePorts() {
    const ports = this.getPorts();
    ports.forEach((port) => {
      this.setPortProp(port.id!, 'attrs/circle/style/visibility', 'hidden');
    });
  }

  // 更新连接桩位置
  private updatePortsPosition() {
    // 当节点尺寸改变时，连接桩位置会自动更新
    // 这里可以添加额外的逻辑
  }

  // 设置节点样式
  setStyle(attrs: any) {
    this.setAttrs(attrs);
  }

  // 设置标签文本
  setLabel(text: string) {
    this.setAttrs({
      text: {
        text,
      },
    });
  }
} 