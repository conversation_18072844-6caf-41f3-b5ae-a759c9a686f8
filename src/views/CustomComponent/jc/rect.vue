<template>
  <svg 
    :width="width" 
    :height="height" 
    :viewBox="`0 0 ${width} ${height}`"
    @mouseenter="showPorts"
    @mouseleave="hidePorts"
  >
    <rect 
      :width="width" 
      :height="height" 
      fill="none" 
      stroke="currentColor" 
      stroke-width="2"
    />
  </svg>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { ports } from '../../../views/Customization/config';

const { node } = defineProps({ node: null });

const width = ref(node?.data?.width || 40);
const height = ref(node?.data?.height || 30);

const showPorts = () => {
  node.setPortProp('', 'attrs/circle/style/visibility', 'visible');
};

const hidePorts = () => {
  node.setPortProp('', 'attrs/circle/style/visibility', 'hidden');
};

onMounted(() => {
  node.on('change:data', ({ cell, current }: any) => {
    cell.size({ width: current.width, height: current.height });
    height.value = current.height;
    width.value = current.width;
  });
});
</script>

<style scoped lang="scss">
svg {
  color: var(--color-primary);
}
</style>