<template>
  <svg :width="width" :height="height" :viewBox="`0 0 ${width} ${height}`">
    <text 
      x="50%" 
      y="50%" 
      text-anchor="middle" 
      dominant-baseline="middle" 
      fill="currentColor"
      font-size="14"
    >
      Text
    </text>
  </svg>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';

const { node } = defineProps({ node: null });

const width = ref(node?.data?.width || 40);
const height = ref(node?.data?.height || 40);

onMounted(() => {
  node.on('change:data', ({ cell, current }: any) => {
    cell.size({ width: current.width, height: current.height });
    height.value = current.height;
    width.value = current.width;
  });
});
</script>

<style scoped lang="scss">
svg {
  color: var(--color-primary);
}
</style>