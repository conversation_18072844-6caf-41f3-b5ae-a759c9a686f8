<template>
  <div class="electrical-icon" :style="{ width: size + 'px', height: size + 'px' }">
    <!-- 晶闸管 -->
    <svg v-if="type === 'thyristor'" :width="size" :height="size" viewBox="0 0 40 60">
      <line x1="20" y1="5" x2="20" y2="15" stroke="currentColor" stroke-width="2" />
      <polygon points="10,15 30,15 20,35" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="35" x2="20" y2="45" stroke="currentColor" stroke-width="2" />
      <line x1="5" y1="25" x2="15" y2="25" stroke="currentColor" stroke-width="2" />
      <circle cx="15" cy="25" r="2" fill="currentColor" />
      <line x1="20" y1="45" x2="20" y2="55" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 二极管 -->
    <svg v-else-if="type === 'diode'" :width="size" :height="size" viewBox="0 0 40 60">
      <line x1="20" y1="5" x2="20" y2="15" stroke="currentColor" stroke-width="2" />
      <polygon points="10,15 30,15 20,35" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="10" y1="35" x2="30" y2="35" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="35" x2="20" y2="55" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 三极管 -->
    <svg v-else-if="type === 'transistor'" :width="size" :height="size" viewBox="0 0 40 60">
      <line x1="15" y1="5" x2="15" y2="55" stroke="currentColor" stroke-width="3" />
      <line x1="15" y1="20" x2="30" y2="10" stroke="currentColor" stroke-width="2" />
      <line x1="15" y1="40" x2="30" y2="50" stroke="currentColor" stroke-width="2" />
      <line x1="30" y1="10" x2="30" y2="5" stroke="currentColor" stroke-width="2" />
      <line x1="30" y1="50" x2="30" y2="55" stroke="currentColor" stroke-width="2" />
      <polygon points="25,45 30,50 25,50" fill="currentColor" />
    </svg>
    
    <!-- 电阻 -->
    <svg v-else-if="type === 'resistor'" :width="size" :height="size" viewBox="0 0 60 40">
      <line x1="5" y1="20" x2="15" y2="20" stroke="currentColor" stroke-width="2" />
      <path d="M15,20 L20,10 L25,30 L30,10 L35,30 L40,10 L45,20" fill="none" stroke="currentColor" stroke-width="2" />
      <line x1="45" y1="20" x2="55" y2="20" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 电容 -->
    <svg v-else-if="type === 'capacitor'" :width="size" :height="size" viewBox="0 0 40 60">
      <line x1="20" y1="5" x2="20" y2="25" stroke="currentColor" stroke-width="2" />
      <line x1="10" y1="25" x2="30" y2="25" stroke="currentColor" stroke-width="3" />
      <line x1="10" y1="35" x2="30" y2="35" stroke="currentColor" stroke-width="3" />
      <line x1="20" y1="35" x2="20" y2="55" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 电感 -->
    <svg v-else-if="type === 'inductor'" :width="size" :height="size" viewBox="0 0 60 40">
      <line x1="5" y1="20" x2="15" y2="20" stroke="currentColor" stroke-width="2" />
      <path d="M15,20 Q20,10 25,20 Q30,10 35,20 Q40,10 45,20" fill="none" stroke="currentColor" stroke-width="2" />
      <line x1="45" y1="20" x2="55" y2="20" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 变压器 -->
    <svg v-else-if="type === 'transformer'" :width="size" :height="size" viewBox="0 0 60 60">
      <circle cx="20" cy="30" r="12" fill="none" stroke="currentColor" stroke-width="2" />
      <circle cx="40" cy="30" r="12" fill="none" stroke="currentColor" stroke-width="2" />
      <line x1="32" y1="15" x2="32" y2="45" stroke="currentColor" stroke-width="2" />
      <line x1="28" y1="15" x2="28" y2="45" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="5" x2="20" y2="18" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="42" x2="20" y2="55" stroke="currentColor" stroke-width="2" />
      <line x1="40" y1="5" x2="40" y2="18" stroke="currentColor" stroke-width="2" />
      <line x1="40" y1="42" x2="40" y2="55" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 开关 -->
    <svg v-else-if="type === 'switch'" :width="size" :height="size" viewBox="0 0 60 40">
      <line x1="5" y1="20" x2="15" y2="20" stroke="currentColor" stroke-width="2" />
      <circle cx="15" cy="20" r="3" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="18" y1="20" x2="42" y2="10" stroke="currentColor" stroke-width="2" />
      <circle cx="45" cy="20" r="3" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="45" y1="20" x2="55" y2="20" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 接地 -->
    <svg v-else-if="type === 'ground'" :width="size" :height="size" viewBox="0 0 40 40">
      <line x1="20" y1="5" x2="20" y2="20" stroke="currentColor" stroke-width="2" />
      <line x1="10" y1="20" x2="30" y2="20" stroke="currentColor" stroke-width="3" />
      <line x1="13" y1="25" x2="27" y2="25" stroke="currentColor" stroke-width="2" />
      <line x1="16" y1="30" x2="24" y2="30" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 电压源 -->
    <svg v-else-if="type === 'voltage_source'" :width="size" :height="size" viewBox="0 0 40 40">
      <circle cx="20" cy="20" r="12" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="16" y1="20" x2="24" y2="20" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="16" x2="20" y2="24" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="5" x2="20" y2="8" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="32" x2="20" y2="35" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 电流源 -->
    <svg v-else-if="type === 'current_source'" :width="size" :height="size" viewBox="0 0 40 40">
      <circle cx="20" cy="20" r="12" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="12" x2="20" y2="28" stroke="currentColor" stroke-width="2" />
      <polygon points="17,25 20,28 23,25" fill="currentColor" />
      <line x1="20" y1="5" x2="20" y2="8" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="32" x2="20" y2="35" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 电机 -->
    <svg v-else-if="type === 'motor'" :width="size" :height="size" viewBox="0 0 50 50">
      <circle cx="25" cy="25" r="18" fill="white" stroke="currentColor" stroke-width="2" />
      <circle cx="25" cy="25" r="12" fill="none" stroke="currentColor" stroke-width="1" />
      <text x="25" y="30" font-size="14" text-anchor="middle" fill="currentColor">M</text>
      <line x1="25" y1="5" x2="25" y2="7" stroke="currentColor" stroke-width="2" />
      <line x1="25" y1="43" x2="25" y2="45" stroke="currentColor" stroke-width="2" />
      <line x1="5" y1="25" x2="7" y2="25" stroke="currentColor" stroke-width="2" />
      <line x1="43" y1="25" x2="45" y2="25" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 发电机 -->
    <svg v-else-if="type === 'generator'" :width="size" :height="size" viewBox="0 0 50 50">
      <circle cx="25" cy="25" r="18" fill="white" stroke="currentColor" stroke-width="2" />
      <circle cx="25" cy="25" r="12" fill="none" stroke="currentColor" stroke-width="1" />
      <text x="25" y="30" font-size="14" text-anchor="middle" fill="currentColor">G</text>
      <line x1="25" y1="5" x2="25" y2="7" stroke="currentColor" stroke-width="2" />
      <line x1="25" y1="43" x2="25" y2="45" stroke="currentColor" stroke-width="2" />
      <line x1="5" y1="25" x2="7" y2="25" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 熔断器 -->
    <svg v-else-if="type === 'fuse'" :width="size" :height="size" viewBox="0 0 60 40">
      <line x1="5" y1="20" x2="15" y2="20" stroke="currentColor" stroke-width="2" />
      <rect x="15" y="15" width="30" height="10" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="20" y1="20" x2="40" y2="20" stroke="currentColor" stroke-width="1" />
      <path d="M25,20 L30,15 L35,25 L40,15" fill="none" stroke="currentColor" stroke-width="1" />
      <line x1="45" y1="20" x2="55" y2="20" stroke="currentColor" stroke-width="2" />
    </svg>
    
    <!-- 继电器 -->
    <svg v-else-if="type === 'relay'" :width="size" :height="size" viewBox="0 0 60 60">
      <rect x="10" y="35" width="20" height="15" fill="white" stroke="currentColor" stroke-width="2" />
      <path d="M15,42 Q20,38 25,42" fill="none" stroke="currentColor" stroke-width="1" />
      <line x1="15" y1="35" x2="15" y2="30" stroke="currentColor" stroke-width="2" />
      <line x1="25" y1="35" x2="25" y2="30" stroke="currentColor" stroke-width="2" />
      <circle cx="40" cy="15" r="3" fill="white" stroke="currentColor" stroke-width="2" />
      <circle cx="50" cy="25" r="3" fill="white" stroke="currentColor" stroke-width="2" />
      <line x1="43" y1="15" x2="47" y2="10" stroke="currentColor" stroke-width="2" />
      <line x1="40" y1="5" x2="40" y2="12" stroke="currentColor" stroke-width="2" />
      <line x1="50" y1="28" x2="50" y2="35" stroke="currentColor" stroke-width="2" />
      <line x1="25" y1="42" x2="40" y2="15" stroke="currentColor" stroke-width="1" stroke-dasharray="2,2" />
    </svg>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';

defineProps({
  // 元件类型
  type: {
    type: String,
    required: true
  },
  // 图标大小
  size: {
    type: Number,
    default: 40
  }
});
</script>

<style scoped lang="scss">
.electrical-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    color: var(--color-primary, #5F95FF);
  }
}
</style> 