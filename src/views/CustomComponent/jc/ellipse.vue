<template>
  <svg :width="width" :height="height" :viewBox="`0 0 ${width} ${height}`">
    <ellipse 
      :cx="width/2" 
      :cy="height/2" 
      :rx="width/2 - 2" 
      :ry="height/2 - 2" 
      fill="none" 
      stroke="currentColor" 
      stroke-width="2"
    />
  </svg>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';

const { node } = defineProps({ node: null });

const width = ref(node?.data?.width || 40);
const height = ref(node?.data?.height || 40);

onMounted(() => {
  node.on('change:data', ({ cell, current }: any) => {
    cell.size({ width: current.width, height: current.height });
    height.value = current.height;
    width.value = current.width;
  });
});
</script>

<style scoped lang="scss">
svg {
  color: var(--color-primary);
}
</style>