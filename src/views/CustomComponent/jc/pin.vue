<template>
  <svg :width="width" :height="height" :viewBox="`0 0 ${width} ${height}`">
    <path 
      d="M20,2 L26,10 L20,18 L14,10 Z" 
      fill="currentColor"
    />
    <circle 
      cx="20" 
      cy="25" 
      r="3" 
      fill="currentColor"
    />
  </svg>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';

const { node } = defineProps({ node: null });

const width = ref(node?.data?.width || 40);
const height = ref(node?.data?.height || 40);

onMounted(() => {
  node.on('change:data', ({ cell, current }: any) => {
    cell.size({ width: current.width, height: current.height });
    height.value = current.height;
    width.value = current.width;
  });
});
</script>

<style scoped lang="scss">
svg {
  color: var(--color-primary);
}
</style>