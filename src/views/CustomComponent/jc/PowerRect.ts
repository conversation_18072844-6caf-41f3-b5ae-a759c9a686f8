import { Graph, Node } from '@antv/x6';
import { ports } from '../../Customization/config';

// 电力元件类型定义
export interface PowerComponentType {
  type: 'transformer' | 'switch' | 'generator' | 'load' | 'capacitor' | 'reactor';
  name: string;
  icon?: string;
  color?: string;
}

// 预定义的电力元件类型
export const POWER_COMPONENTS: Record<string, PowerComponentType> = {
  transformer: {
    type: 'transformer',
    name: '变压器',
    icon: '🔌',
    color: '#FF6B6B',
  },
  switch: {
    type: 'switch',
    name: '开关',
    icon: '🔘',
    color: '#4ECDC4',
  },
  generator: {
    type: 'generator',
    name: '发电机',
    icon: '⚡',
    color: '#45B7D1',
  },
  load: {
    type: 'load',
    name: '负荷',
    icon: '💡',
    color: '#96CEB4',
  },
  capacitor: {
    type: 'capacitor',
    name: '电容器',
    icon: '🔋',
    color: '#FFEAA7',
  },
  reactor: {
    type: 'reactor',
    name: '电抗器',
    icon: '🌀',
    color: '#DDA0DD',
  },
};

// 注册电力矩形节点
Graph.registerNode(
  'power-rect',
  {
    inherit: 'rect',
    width: 100,
    height: 60,
    attrs: {
      body: {
        strokeWidth: 2,
        stroke: '#5F95FF',
        fill: '#EFF4FF',
        rx: 6,
        ry: 6,
      },
      text: {
        fontSize: 12,
        fill: '#262626',
        textAnchor: 'middle',
        textVerticalAnchor: 'middle',
        fontWeight: 'bold',
      },
      // 添加图标元素
      icon: {
        fontSize: 16,
        fill: '#5F95FF',
        textAnchor: 'middle',
        textVerticalAnchor: 'middle',
        x: 20,
        y: 20,
      },
      // 添加状态指示器
      status: {
        r: 4,
        fill: '#52C41A',
        stroke: '#fff',
        strokeWidth: 1,
        cx: 85,
        cy: 15,
      },
    },
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'text',
        selector: 'icon',
      },
      {
        tagName: 'text',
        selector: 'text',
      },
      {
        tagName: 'circle',
        selector: 'status',
      },
    ],
    ports: {
      ...ports,
    },
  },
  true
);

// 电力矩形节点类
export class PowerRectNode extends Node {
  private componentType: PowerComponentType;
  private status: 'online' | 'offline' | 'fault' = 'online';

  constructor(options: {
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    componentType: keyof typeof POWER_COMPONENTS;
    label?: string;
    id?: string;
  }) {
    const {
      x = 100,
      y = 100,
      width = 100,
      height = 60,
      componentType,
      label,
      id,
    } = options;

    const component = POWER_COMPONENTS[componentType];
    
    super({
      id,
      shape: 'power-rect',
      x,
      y,
      width,
      height,
      attrs: {
        body: {
          fill: component.color + '20', // 添加透明度
          stroke: component.color,
        },
        text: {
          text: label || component.name,
        },
        icon: {
          text: component.icon || '',
        },
      },
      ports: {
        ...ports,
      },
      data: {
        componentType: component.type,
        status: 'online',
      },
    });

    this.componentType = component;
    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    // 鼠标悬停显示连接桩
    this.on('mouseenter', () => {
      this.showPorts();
      this.showTooltip();
    });

    // 鼠标离开隐藏连接桩
    this.on('mouseleave', () => {
      this.hidePorts();
      this.hideTooltip();
    });

    // 双击编辑
    this.on('cell:dblclick', () => {
      this.openEditDialog();
    });

    // 右键菜单
    this.on('cell:contextmenu', ({ e }: { e: any }) => {
      this.showContextMenu(e);
    });
  }

  // 显示连接桩
  showPorts() {
    const ports = this.getPorts();
    ports.forEach((port) => {
      this.setPortProp(port.id!, 'attrs/circle/style/visibility', 'visible');
    });
  }

  // 隐藏连接桩
  hidePorts() {
    const ports = this.getPorts();
    ports.forEach((port) => {
      this.setPortProp(port.id!, 'attrs/circle/style/visibility', 'hidden');
    });
  }

  // 设置状态
  setStatus(status: 'online' | 'offline' | 'fault') {
    this.status = status;
    const statusColors = {
      online: '#52C41A',
      offline: '#8C8C8C',
      fault: '#FF4D4F',
    };

    this.setAttrs({
      status: {
        fill: statusColors[status],
      },
    });

    this.setData({ ...this.getData(), status });
  }

  // 获取状态
  getStatus() {
    return this.status;
  }

  // 获取组件类型
  getComponentType() {
    return this.componentType;
  }

  // 设置标签
  setLabel(text: string) {
    this.setAttrs({
      text: {
        text,
      },
    });
  }

  // 显示工具提示
  private showTooltip() {
    // 这里可以实现工具提示功能
    console.log(`${this.componentType.name} - 状态: ${this.status}`);
  }

  // 隐藏工具提示
  private hideTooltip() {
    // 隐藏工具提示
  }

  // 打开编辑对话框
  private openEditDialog() {
    // 这里可以实现编辑对话框
    console.log('打开编辑对话框');
  }

  // 显示右键菜单
  private showContextMenu(e: any) {
    e.preventDefault();
    // 这里可以实现右键菜单
    console.log('显示右键菜单');
  }

  // 获取连接信息
  getConnections() {
    const graph = this.model?.graph;
    if (!graph) return [];

    return graph.getConnectedEdges(this);
  }

  // 检查是否可以连接到指定节点
  canConnectTo(targetNode: PowerRectNode): boolean {
    // 这里可以添加电力系统的连接规则
    // 例如：变压器可以连接到任何元件，但负荷不能直接连接到负荷
    const sourceType = this.componentType.type;
    const targetType = targetNode.getComponentType().type;

    // 简单的连接规则示例
    if (sourceType === 'load' && targetType === 'load') {
      return false; // 负荷不能直接连接到负荷
    }

    return true;
  }
}

// 创建电力矩形节点的工厂函数
export function createPowerRectNode(options: {
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  componentType: keyof typeof POWER_COMPONENTS;
  label?: string;
  id?: string;
}) {
  return new PowerRectNode(options);
} 