<template>
  <div class="dynamic-json-component">
    <!-- 这是一个占位组件，用于JSON组合组件的注册 -->
    <!-- 实际的内部结构会通过X6 Group节点自动恢复 -->
    <div class="placeholder">
      <span>JSON组合组件</span>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这个组件主要用于组件注册，实际渲染由X6 Group节点处理
defineProps<{
  // 组件数据
  data?: any;
  // 组件配置
  config?: any;
}>();
</script>

<style scoped>
.dynamic-json-component {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #ccc;
  background: rgba(255, 255, 255, 0.8);
}

.placeholder {
  font-size: 12px;
  color: #666;
  text-align: center;
}
</style>