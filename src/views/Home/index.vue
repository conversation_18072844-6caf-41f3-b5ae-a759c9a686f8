<template>
  <div class="home-container">
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>素材更新</span>
            </div>
          </template>
          <div class="material-update">
            <img src="@/assets/images/znjhImg/guangfu.svg" alt="光伏储能系统" />
            <div class="material-info">
              <h3>光伏储能系统</h3>
              <el-tag type="danger">上新</el-tag>
            </div>
          </div>
        </el-card>
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>典型案例</span>
            </div>
          </template>
          <div v-for="o in 6" :key="o" class="case-item">
            <div class="case-info">
              <el-icon><Document /></el-icon>
              <div>
                <p>110kV变电站二次系统图</p>
                <span>标准二/标准三</span>
              </div>
            </div>
            <div class="case-version">
              <span>V1.0.1</span>
              <span>2025/6/17 12:23:56</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>新闻资讯</span>
            </div>
          </template>
          <div v-for="o in 6" :key="o" class="news-item">
            <p>新闻标题 {{ o }}</p>
            <span>2025-06-17</span>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { Document } from '@element-plus/icons-vue';
</script>

<style scoped>
.home-container {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.box-card {
  margin-bottom: 20px;
}
.material-update {
  display: flex;
  align-items: center;
  gap: 20px;
}
.material-update img {
  width: 120px;
  height: 120px;
}
.case-item, .news-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e6e6e6;
}
.case-item:last-child, .news-item:last-child {
  border-bottom: none;
}
.case-info {
  display: flex;
  align-items: center;
  gap: 10px;
}
.case-info .el-icon {
  font-size: 32px;
}
.case-version {
  text-align: right;
  color: #999;
}
</style>