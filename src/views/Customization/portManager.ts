import { Node, Graph } from '@antv/x6';

// 引脚配置
export interface CustomPort {
  id: string;
  group: 'absolute';
  args: {
    position: {
      x: number | string;
      y: number | string;
    };
  };
  attrs: {
    circle: {
      r: number;
      magnet: true;
      stroke: string;
      strokeWidth: number;
      fill: string;
      style?: {
        visibility?: string;
        cursor?: string;
      };
    };
    text?: {
      text: string;
      fill: string;
      fontSize: number;
    };
  };
  label?: {
    position: {
      name: string;
      args?: {
        offset?: number;
      };
    };
  };
  markup?: any[];
}

// 创建自定义引脚
export function createCustomPort(x: number | string, y: number | string, label?: string): CustomPort {
  const portId = `port-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  return {
    id: portId,
    group: 'absolute',
    args: {
      position: { x, y },
    },
    markup: [
      {
        tagName: 'circle',
        selector: 'circle',
      },
    ],
    attrs: {
      circle: {
        r: 4,  // 减小引脚大小
        magnet: true,
        stroke: '#5F95FF',
        strokeWidth: 1,
        fill: '#fff',
        style: {
          visibility: 'visible',
          cursor: 'crosshair',
        },
      },
      ...(label && {
        text: {
          text: label,
          fill: '#5F95FF',
          fontSize: 10,
        },
      }),
    },
    ...(label && {
      label: {
        position: {
          name: 'outside',
          args: {
            offset: 10,
          },
        },
      },
    }),
  };
}

// 引脚管理器类
export class PortManager {
  private graph: Graph;
  private isAddingPort: boolean = false;
  // private selectedNode: Node | null = null;

  constructor(graph: Graph) {
    this.graph = graph;
    this.setupEventListeners();
  }

  // 设置事件监听
  private setupEventListeners() {
    // 监听节点点击
    this.graph.on('node:click', ({ node, e }) => {
      if (this.isAddingPort) {
        e.stopPropagation();
        e.preventDefault();
        this.addPortToNode(node, e as any);
      }
    });

    // 监听节点边缘点击
    this.graph.on('node:mousedown', ({ node, e }) => {
      if (this.isAddingPort) {
        e.stopPropagation();
        e.preventDefault();
        this.addPortToNode(node, e as any);
      }
    });

    // 监听引脚的显示/隐藏
    this.graph.on('node:mouseenter', ({ node }) => {
      this.showNodePorts(node);
    });

    this.graph.on('node:mouseleave', ({ node }) => {
      this.hideNodePorts(node);
    });
  }

  // 启用/禁用添加引脚模式
  toggleAddPortMode(enabled: boolean) {
    this.isAddingPort = enabled;
    if (enabled) {
      // 改变鼠标样式
      const container = this.graph.container;
      if (container) {
        container.style.cursor = 'crosshair';
      }
    } else {
      const container = this.graph.container;
      if (container) {
        container.style.cursor = 'default';
      }
    }
  }

  // 添加引脚到节点
  addPortToNode(node: Node, e: MouseEvent) {
    const { x, y } = this.graph.clientToLocal(e.clientX, e.clientY);
    const nodeBBox = node.getBBox();
    
    // 计算相对于节点的位置
    const relativeX = x - nodeBBox.x;
    const relativeY = y - nodeBBox.y;
    
    // 将位置限制在节点边缘
    const position = this.snapToEdge(relativeX, relativeY, nodeBBox.width, nodeBBox.height);
    
    // 创建新的引脚
    const port = createCustomPort(position.x, position.y);
    
    // 添加引脚到节点
    node.addPort(port);
    
    // 触发引脚添加事件
    this.graph.trigger('port:added', { node, port });
  }

  // 将位置吸附到边缘
  private snapToEdge(x: number, y: number, width: number, height: number) {
    const threshold = 20; // 吸附阈值
    
    // 检查距离哪条边最近
    const distances = [
      { edge: 'top', distance: y, x: x, y: 0 },
      { edge: 'right', distance: width - x, x: width, y: y },
      { edge: 'bottom', distance: height - y, x: x, y: height },
      { edge: 'left', distance: x, x: 0, y: y },
    ];
    
    // 找到最近的边
    const nearest = distances.reduce((min, curr) => 
      curr.distance < min.distance ? curr : min
    );
    
    // 如果距离小于阈值，吸附到边缘
    if (nearest.distance < threshold) {
      return { x: nearest.x, y: nearest.y };
    }
    
    // 否则返回原始位置
    return { x, y };
  }

  // 显示节点的所有引脚
  showNodePorts(node: Node) {
    const ports = node.getPorts();
    ports.forEach((port) => {
      node.setPortProp(port.id!, 'attrs/circle/style/visibility', 'visible');
    });
  }

  // 隐藏节点的所有引脚
  hideNodePorts(node: Node) {
    const ports = node.getPorts();
    ports.forEach((port) => {
      // 只隐藏默认引脚，自定义引脚保持可见
      if (!port.id?.startsWith('port-')) {
        node.setPortProp(port.id!, 'attrs/circle/style/visibility', 'hidden');
      }
    });
  }

  // 删除引脚
  removePort(node: Node, portId: string) {
    node.removePort(portId);
  }

  // 获取节点的所有自定义引脚
  getCustomPorts(node: Node): any[] {
    const ports = node.getPorts();
    return ports.filter(port => port.id?.startsWith('port-'));
  }

  // 更新引脚位置
  updatePortPosition(node: Node, portId: string, x: number | string, y: number | string) {
    node.setPortProp(portId, 'args/position', { x, y });
  }

  // 批量添加引脚
  addMultiplePorts(node: Node, positions: Array<{ x: number | string; y: number | string; label?: string }>) {
    const ports = positions.map(pos => createCustomPort(pos.x, pos.y, pos.label));
    node.addPorts(ports);
  }

  // 清除节点的所有自定义引脚
  clearCustomPorts(node: Node) {
    const customPorts = this.getCustomPorts(node);
    customPorts.forEach(port => {
      if (port.id) {
        this.removePort(node, port.id);
      }
    });
  }
} 