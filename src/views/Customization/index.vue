<template>
  <article>
    <header class="header">
      <div class="header-left">
        <img src="@/assets/images/logo.png" alt="Logo" class="logo" />
        <h1 class="title">电力元件编辑器</h1>
      </div>
      <div class="header-right">
        <el-button @click="showGenerateDialog">批量生成元件</el-button>
        <el-button @click="clearX6">清空画布</el-button>
        <el-button @click="deleteClick">删除组件</el-button>
        <el-button type="primary" @click="save">保存</el-button>
        <el-button
          v-if="0"
          :type="isAddingPort ? 'success' : 'default'"
          @click="togglePortMode"
        >
          {{ isAddingPort ? "退出引脚模式" : "引脚模式" }}
        </el-button>
      </div>
    </header>
    <main class="main">
      <div class="left">
        <Left></Left>
      </div>
      <div class="center">
        <div id="container"></div>
      </div>
      <div class="right">
        <Right></Right>
      </div>
    </main>

    <!-- 批量生成对话框 -->
    <el-dialog
      v-model="generateDialogVisible"
      title="批量生成电力元件"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form :model="generateForm" label-width="100px">
        <el-form-item label="元件数量">
          <el-input-number
            v-model="generateForm.count"
            :min="1"
            :max="1000"
            :step="10"
            placeholder="请输入元件数量"
          />
          <span style="margin-left: 10px; color: #909399">建议不超过400个</span>
        </el-form-item>
        <el-form-item label="布局方式">
          <el-radio-group v-model="generateForm.layout">
            <el-radio label="grid">网格布局</el-radio>
            <el-radio label="random">随机布局</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="连接数量">
          <el-input-number
            v-model="generateForm.connectionCount"
            :min="0"
            :max="generateForm.count * 3"
            :step="10"
            placeholder="请输入连接数量"
          />
          <span style="margin-left: 10px; color: #909399"
            >建议为元件数量的1.5倍</span
          >
        </el-form-item>
        <el-form-item label="连接密度">
          <el-slider
            v-model="generateForm.connectionDensity"
            :min="0"
            :max="100"
            :format-tooltip="(val: any) => `${val}%`"
          />
          <span style="margin-left: 10px; color: #909399"
            >控制连接的分布密度</span
          >
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="generateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmGenerate"> 生成 </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 右键菜单 -->
    <div
      v-show="contextMenuVisible"
      ref="contextMenuRef"
      class="context-menu"
      :style="{ left: contextMenuPosition.x + 'px', top: contextMenuPosition.y + 'px' }"
      @click.stop
    >
      <div class="context-menu-item" @click="bringToFront">
        <i class="el-icon-top"></i>
        <span>置于顶层</span>
      </div>
      <div class="context-menu-item" @click="sendToBack">
        <i class="el-icon-bottom"></i>
        <span>置于底层</span>
      </div>
      <div class="context-menu-divider"></div>
      <div class="context-menu-item" @click="bringForward">
        <i class="el-icon-arrow-up"></i>
        <span>上移一层</span>
      </div>
      <div class="context-menu-item" @click="sendBackward">
        <i class="el-icon-arrow-down"></i>
        <span>下移一层</span>
      </div>
      <div class="context-menu-divider"></div>
      <div class="context-menu-item context-menu-item-danger" @click="deleteSelectedNode">
        <i class="el-icon-delete"></i>
        <span>删除</span>
      </div>
    </div>
  </article>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, provide, ref, watch, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Graph } from "@antv/x6";
import { Keyboard } from '@antv/x6-plugin-keyboard';
import { Export } from "@antv/x6-plugin-export";
import { Transform } from "@antv/x6-plugin-transform";
import { Snapline } from "@antv/x6-plugin-snapline";
import { Selection } from "@antv/x6-plugin-selection";
//import { MiniMap } from "@antv/x6-plugin-minimap";
import Left from "./Left/index.vue";
import Right from "./Right/index.vue";
import registerInit, { registerDynamicSvg, registerDynamicJsonComponent } from "./register";
import { useX6Store } from "@/store";
import { addDynamicComponent } from './Left/customImg';
import type { UserComponent } from "@/store/modules/useX6";
import { connectLine, ports } from "./config";
import { PortManager } from "./portManager";
import { AdvancedDragPortManager } from "./advancedDragPortManager";

const x6Store = useX6Store();

// 注册自定义组件
registerInit();

// 确保所有用户组件都已注册
const initAllUserComponents = () => {
  x6Store.userComponents.forEach((comp: UserComponent) => {
    try {
      registerDynamicSvg(comp.id, comp.svgData, {
        width: 120,
        height: 80,
        name: comp.name
      });
    } catch (error) {
      console.warn(`注册用户组件 ${comp.name} 失败:`, error);
    }
  });
};

// 初始化时注册所有用户组件
initAllUserComponents();

// x6画布
const graph = ref<any>({});
// 引脚管理器
let portManager: PortManager;
// 拖拽引脚管理器
let dragPortManager: AdvancedDragPortManager;
// 引脚模式状态
const isAddingPort = ref(false);
// 批量生成对话框
const generateDialogVisible = ref(false);
const generateForm = ref({
  count: 100,
  layout: "grid",
  connectionCount: 150,
  connectionDensity: 30,
});

// 右键菜单相关
const contextMenuVisible = ref(false);
const contextMenuPosition = ref({ x: 0, y: 0 });
const selectedNode = ref<any>(null);
const contextMenuRef = ref<HTMLElement>();
const init = () => {
  graph.value = new Graph({
    container: document.getElementById("container")!,
    autoResize: true,
    panning: {
      enabled: true,
      eventTypes: ["leftMouseDown", "rightMouseDown"], // 支持左键和右键拖拽
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: "ctrl",
      minScale: 0.1,
      maxScale: 10,
    },
    // 画布大小设置
    width: document.getElementById("container")!.offsetWidth,
    height: document.getElementById("container")!.offsetHeight,
    background: {
      color: "#F2F3F5",
    },
    grid: {
      visible: true,
      //type: "dot",
      type: "doubleMesh",
      args: [
        {
          color: "#e0e0e0",
          thickness: 1,
        },
        {
          color: "#e0e0e0",
          thickness: 1,
          factor: 4,
        },
      ],
    },
    embedding: {
      enabled: true,
      findParent({ node }) {
        const bbox = node.getBBox();
        return this.getNodes().filter((node) => {
          const data = node.getData<any>();
          if (data && data.parent) {
            const targetBBox = node.getBBox();
            return bbox.isIntersectWithRect(targetBBox);
          }
          return false;
        });
      },
    },
    highlighting: {
      embedding: {
        name: "stroke",
        args: {
          padding: -1,
          attrs: {
            stroke: "#73d13d",
          },
        },
      },
    },
    connecting: {
      snap: true,
      allowBlank: false,
      allowLoop: false,
      highlight: true,
      connector: {
        name: "normal", // 使用直线连接器
      },
      connectionPoint: "anchor",
      anchor: "center",
      router: {
        name: "orth", // 使用正交路由
        args: {
          padding: 10,
        },
      },
      createEdge() {
        const edge = graph.value.createEdge({
          ...connectLine,
        });
        // 强制移除箭头
        edge.attr("line/targetMarker", null);
        return edge;
      },
      validateConnection({
        sourceView,
        targetView,
        sourceMagnet,
        targetMagnet,
      }) {
        // 不允许连接到自身
        if (sourceView === targetView) {
          return false;
        }
        // 必须从磁铁（连接桩）连接到磁铁
        return !!sourceMagnet && !!targetMagnet;
      },
    },
  });

  // 添加导出插件
  graph.value.use(new Export());

  // 添加变换插件（缩放、旋转）
  graph.value.use(
    new Transform({
      resizing: {
        enabled: true,
        minWidth: 30,
        minHeight: 30,
        maxWidth: 500,
        maxHeight: 500,
        orthogonal: false, // 是否保持宽高比
        restrict: false,
        preserveAspectRatio: false,
      },
      rotating: {
        enabled: true,
        grid: 15, // 旋转角度的步进
      },
    })
  );

  graph.value.use(new Keyboard({
    enabled: true,
  }
    
  ));

  // 添加对齐线插件
  graph.value.use(
    new Snapline({
      enabled: true,
      sharp: true,
      resizing: true,
      // moving: true, // 移除不支持的选项
    })
  );

  // 添加框选插件
  graph.value.use(
    new Selection({
      enabled: true,
      multiple: true,
      rubberband: true, // 启用框选
      movable: true,
      showNodeSelectionBox: true,
      modifiers: ["shift"], // 按住 shift 多选
      rubberNode: true,
      rubberEdge: false, // 不框选边
      pointerEvents: "none",
    })
  );

  // 添加小地图插件
  const minimapContainer = document.createElement("div");
  minimapContainer.style.position = "absolute";
  minimapContainer.style.bottom = "20px";
  minimapContainer.style.right = "20px";
  minimapContainer.style.width = "200px";
  minimapContainer.style.height = "150px";
  minimapContainer.style.border = "1px solid #ddd";
  minimapContainer.style.borderRadius = "4px";
  minimapContainer.style.boxShadow = "0 2px 8px rgba(0,0,0,0.1)";
  minimapContainer.style.background = "#fff";
  minimapContainer.style.zIndex = "10";
  //document.getElementById('container')?.appendChild(minimapContainer);

  // graph.value.use(
  //   new MiniMap({
  //     container: minimapContainer,
  //     width: 200,
  //     height: 150,
  //     padding: 10,
  //     scalable: true,
  //     minScale: 0.01,
  //     maxScale: 16,
  //   })
  // );

  // 初始化引脚管理器
  portManager = new PortManager(graph.value);

  // 初始化拖拽引脚管理器
  dragPortManager = new AdvancedDragPortManager(graph.value);

  // 在回显数据前再次确保所有用户组件都已注册
  initAllUserComponents();

  // 回显已保存画布
  if (x6Store.initJSON) {
    try {
      graph.value.fromJSON(x6Store.initJSON);
    } catch (error) {
      console.error('数据回显失败:', error);
      // 如果回显失败，尝试重新注册组件后再次回显
      setTimeout(() => {
        initAllUserComponents();
        try {
          graph.value.fromJSON(x6Store.initJSON);
        } catch (retryError) {
          console.error('重试数据回显仍然失败:', retryError);
        }
      }, 100);
    }
  }

  // 点击当前节点执行
  graph.value.on("node:click", ({ cell }: any) => {
    // 如果在添加引脚模式，不执行节点选择
    if (isAddingPort.value) {
      return;
    }
    x6Store.currentNodeChange(cell);

    // 移除其他节点的工具
    const cells = graph.value.getCells();
    cells.forEach((c: any) => {
      if (c.isNode() && c !== cell) {
        c.removeTools();
      }
    });

    // 添加变换工具和删除按钮
    cell.addTools([
      {
        name: "button-remove",
        args: {
          x: "100%",
          y: -10,
          offset: { x: 0, y: 0 },
        },
      },
      {
        name: "boundary",
        args: {
          padding: 5,
          attrs: {
            fill: "none",
            stroke: "#5F95FF",
            strokeWidth: 1,
            strokeDasharray: "5 5",
            pointerEvents: "none",
          },
        },
      },
    ]);
  });

  // 节点删除执行
  graph.value.on("node:removed", () => {
    x6Store.currentNodeChange(null);
  });

  // 节点添加执行
  graph.value.on("node:added", ({ node }: any) => {
    // 处理JSON组合组件
    if (node.getData()?.needsInternalStructure && node.getData()?.componentInfo) {
      console.log('检测到JSON组合组件，开始恢复内部结构:', node.getData());
      
      const componentInfo = node.getData().componentInfo;
      const nodePosition = node.getPosition();
      const nodeSize = node.getSize();
      
      // 提取内部节点和边
      const internalNodes = componentInfo.nodes || [];
      const internalEdges = componentInfo.edges || [];
      
      console.log(`恢复 ${internalNodes.length} 个内部节点和 ${internalEdges.length} 条内部边`);
      
      if (internalNodes.length > 0) {
        // 计算内部结构的包围盒
        const internalBBox = getCellsBBox(internalNodes);
        
        // 计算缩放比例和偏移量，使内部结构适应Group容器
        const scaleX = (nodeSize.width - 40) / internalBBox.width; // 留出边距
        const scaleY = (nodeSize.height - 40) / internalBBox.height;
        const scale = Math.min(scaleX, scaleY, 1); // 不放大，只缩小
        
        const offsetX = nodePosition.x + 20; // Group容器内的偏移
        const offsetY = nodePosition.y + 30; // 为标题留出空间
        
        // 生成新的ID映射
        const idMap: Record<string, string> = {};
        
        // 处理内部节点
        const processedNodes = internalNodes.map((nodeData: any) => {
          const newId = generateId();
          idMap[nodeData.id] = newId;
          
          const scaledX = (nodeData.position.x - internalBBox.x) * scale + offsetX;
          const scaledY = (nodeData.position.y - internalBBox.y) * scale + offsetY;
          
          return {
            ...nodeData,
            id: newId,
            position: { x: scaledX, y: scaledY },
            size: {
              width: (nodeData.size?.width || 40) * scale,
              height: (nodeData.size?.height || 40) * scale
            },
            parent: node.id // 设置父节点为Group
          };
        });
        
        // 处理内部边
        const processedEdges = internalEdges.map((edgeData: any) => {
          const newId = generateId();
          
          return {
            ...edgeData,
            id: newId,
            source: {
              ...edgeData.source,
              cell: idMap[edgeData.source.cell] || edgeData.source.cell
            },
            target: {
              ...edgeData.target,
              cell: idMap[edgeData.target.cell] || edgeData.target.cell
            },
            parent: node.id // 设置父节点为Group
          };
        });
        
        // 清除needsInternalStructure标志，避免重复处理
        node.setData({
          ...node.getData(),
          needsInternalStructure: false,
          internalNodesRestored: true
        });
        
        // 添加内部结构到画布
        const allInternalCells = [...processedNodes, ...processedEdges];
        graph.value.addCells(allInternalCells);
        
        console.log('✅ JSON组合组件内部结构恢复完成');
      }
    }
    // 处理传统用户组件（保持兼容性）
    else if (node.shape === "user-component" && node.getData()?.isUserComponent) {
      const jsonData = node.getData().jsonData;
      if (jsonData && jsonData.cells && jsonData.cells.length > 0) {
        // 获取节点位置
        const nodePosition = node.getPosition();

        // 计算整体包围盒
        const bbox = getCellsBBox(jsonData.cells);
        const dx = nodePosition.x - (bbox.x + bbox.width / 2);
        const dy = nodePosition.y - (bbox.y + bbox.height / 2);

        // 复制 cells，生成新 id，并整体平移
        const idMap: Record<string, string> = {};
        const newCells = jsonData.cells
          .map((cell: any) => {
            const newCell = { ...cell };
            newCell.id = generateId();
            idMap[cell.id] = newCell.id;
            if (cell.shape && !cell.isEdge) {
              // 兼容 position 字段
              const oldX = cell.position?.x ?? cell.x ?? 0;
              const oldY = cell.position?.y ?? cell.y ?? 0;
              const width = cell.size?.width ?? cell.width ?? 0;
              const height = cell.size?.height ?? cell.height ?? 0;
              newCell.position = {
                x: oldX + dx,
                y: oldY + dy,
              };
              newCell.size = { width, height };
              // 移除 x/y 字段，避免冲突
              delete newCell.x;
              delete newCell.y;
            }
            return newCell;
          })
          .map((cell: any) => {
            // 边的 source/target id 替换为新 id
            if (cell.shape && cell.isEdge) {
              if (cell.source && cell.source.cell) {
                cell.source.cell = idMap[cell.source.cell] || cell.source.cell;
              }
              if (cell.target && cell.target.cell) {
                cell.target.cell = idMap[cell.target.cell] || cell.target.cell;
              }
            }
            return cell;
          });

        // 移除临时节点
        graph.value.removeNode(node);

        // 添加用户组件的所有cells
        graph.value.fromJSON({ cells: newCells });
      }
    }
  });

  // 点击画布执行
  graph.value.on("blank:click", () => {
    // 移除所有节点的工具
    const cells = graph.value.getCells();
    cells.forEach((cell: any) => {
      if (cell.isNode()) {
        cell.removeTools();
      }
    });
    x6Store.currentNodeChange(null);
  });

  // 鼠标 Hover 时执行 - 显示连接桩
  graph.value.on("node:mouseenter", ({ node }: any) => {
    // 只对有连接桩的节点显示
    const ports = node.getPorts();
    if (ports && ports.length > 0) {
      ports.forEach((port: any) => {
        node.setPortProp(port.id, "attrs/circle/style/visibility", "visible");
      });
    }
  });

  // 鼠标移开时执行 - 隐藏连接桩
  graph.value.on("node:mouseleave", ({ node }: any) => {
    // 只对有连接桩的节点隐藏
    const ports = node.getPorts();
    if (ports && ports.length > 0) {
      ports.forEach((port: any) => {
        node.setPortProp(port.id, "attrs/circle/style/visibility", "hidden");
      });
    }
  });

  //Delete 键删除选中连线
  graph.value.bindKey(['delete', 'backspace'], () => {
  const selectedEdges = graph.value.getSelectedCells().filter((cell: any) => cell.isEdge());
  selectedEdges.forEach((edge: any) => edge.remove());
});

  // 右键连线删除
  graph.value.on('edge:contextmenu', ({ edge }: any) => {
    edge.remove();
  });

  // 右键节点菜单
  graph.value.on('node:contextmenu', ({ node, e }: any) => {
    e.preventDefault();
    showNodeContextMenu(node, e);
  });

  // 点击画布隐藏右键菜单
  graph.value.on('blank:click', () => {
    hideContextMenu();
  });

  // 画布滚动时隐藏右键菜单
  graph.value.on('scale', () => {
    hideContextMenu();
  });

  graph.value.on('translate', () => {
    hideContextMenu();
  });
};

// 切换引脚模式
const togglePortMode = () => {
  isAddingPort.value = !isAddingPort.value;
  portManager.toggleAddPortMode(isAddingPort.value);

  if (isAddingPort.value) {
    ElMessage({
      message: "进入引脚添加模式，点击节点边缘添加引脚",
      type: "info",
    });
  }
};

// 显示生成对话框
const showGenerateDialog = () => {
  generateDialogVisible.value = true;
};

// 确认生成
const confirmGenerate = () => {
  generateDialogVisible.value = false;
  generateTestDiagram(generateForm.value);
};

// 生成测试图
const generateTestDiagram = (
  options = {
    count: 100,
    layout: "grid",
    connectionCount: 150,
    connectionDensity: 30,
  }
) => {
  try {
    // 性能警告
    if (options.count > 400) {
      ElMessage.warning("元件数量较多，生成过程可能需要几秒钟，请耐心等待...");
    }

    // 清空现有内容
    graph.value.clearCells();

    // // 暂时禁用某些功能以提升性能
    // if (options.count > 500) {
    //   // 禁用一些交互以提升性能
    //   graph.value.disableSelection?.();
    //   graph.value.disableKeyboard?.();
    // }

    // 可用的元件类型 - 使用更简单的类型
    const nodeTypes = [
      "thyristor",
      "diode",
      "transistor",
      "resistor",
      "capacitor",
      "inductor",
      "transformer",
      "switch",
      "ground",
      "voltage_source",
      "current_source",
      "motor",
      "generator",
      "fuse",
    ];

    // 生成的节点数组
    const nodes: any[] = [];
    const nodeMap = new Map(); // 用于快速查找节点

    // 计算布局参数
    const count = options.count;
    const cols = Math.ceil(Math.sqrt(count * 1.5)); // 列数稍多一些，让布局更宽
    const rows = Math.ceil(count / cols); // 行数
    const cellWidth = Math.max(80, Math.min(150, 1500 / cols)); // 自适应单元格宽度
    const cellHeight = Math.max(60, Math.min(120, 1000 / rows)); // 自适应单元格高度
    const startX = 50;
    const startY = 50;

    // 生成节点配置数组
    console.log(`开始生成${count}个节点...`);
    const nodeConfigs = [];

    // electrical 元件类型到 ports.items 的映射
    const electricalPortsMap: Record<string, any[]> = {
      thyristor: [
        { id: 'top', group: 'absolute', args: { x: 20, y: 4 } },
        { id: 'bottom', group: 'absolute', args: { x: 20, y: 56 } },
        { id: 'left', group: 'absolute', args: { x: 4, y: 25 } },
      ],
      diode: [
        { id: 'top', group: 'absolute', args: { x: 20, y: 5 } },
        { id: 'bottom', group: 'absolute', args: { x: 20, y: 55 } },
      ],
      transistor: [
        { id: 'top', group: 'absolute', args: { x: 15, y: 5 } },
        { id: 'bottom', group: 'absolute', args: { x: 15, y: 55 } },
        { id: 'rightTop', group: 'absolute', args: { x: 30, y: 5 } },
        { id: 'rightBottom', group: 'absolute', args: { x: 30, y: 55 } },
      ],
      resistor: [
        { id: 'left', group: 'absolute', args: { x: 5, y: 20 } },
        { id: 'right', group: 'absolute', args: { x: 55, y: 20 } },
      ],
      capacitor: [
        { id: 'top', group: 'absolute', args: { x: 20, y: 5 } },
        { id: 'bottom', group: 'absolute', args: { x: 20, y: 55 } },
      ],
      inductor: [
        { id: 'left', group: 'absolute', args: { x: 5, y: 20 } },
        { id: 'right', group: 'absolute', args: { x: 55, y: 20 } },
      ],
      transformer: [
        { id: 'leftTop', group: 'absolute', args: { x: 20, y: 5 } },
        { id: 'leftBottom', group: 'absolute', args: { x: 20, y: 55 } },
        { id: 'rightTop', group: 'absolute', args: { x: 40, y: 5 } },
        { id: 'rightBottom', group: 'absolute', args: { x: 40, y: 55 } },
      ],
      switch: [
        { id: 'left', group: 'absolute', args: { x: 5, y: 20 } },
        { id: 'right', group: 'absolute', args: { x: 55, y: 20 } },
      ],
      ground: [
        { id: 'top', group: 'absolute', args: { x: 20, y: 5 } },
      ],
      voltage_source: [
        { id: 'top', group: 'absolute', args: { x: 20, y: 5 } },
        { id: 'bottom', group: 'absolute', args: { x: 20, y: 35 } },
      ],
      current_source: [
        { id: 'top', group: 'absolute', args: { x: 20, y: 5 } },
        { id: 'bottom', group: 'absolute', args: { x: 20, y: 35 } },
      ],
      motor: [
        { id: 'top', group: 'absolute', args: { x: 25, y: 5 } },
        { id: 'bottom', group: 'absolute', args: { x: 25, y: 45 } },
        { id: 'left', group: 'absolute', args: { x: 5, y: 25 } },
        { id: 'right', group: 'absolute', args: { x: 45, y: 25 } },
      ],
      generator: [
        { id: 'top', group: 'absolute', args: { x: 25, y: 5 } },
        { id: 'left', group: 'absolute', args: { x: 5, y: 25 } },
      ],
      fuse: [
        { id: 'left', group: 'absolute', args: { x: 5, y: 20 } },
        { id: 'right', group: 'absolute', args: { x: 55, y: 20 } },
      ],
      relay: [
        { id: 'left', group: 'absolute', args: { x: 5, y: 30 } },
        { id: 'right', group: 'absolute', args: { x: 55, y: 30 } },
      ],
    };

    for (let i = 0; i < count; i++) {
      const row = Math.floor(i / cols);
      const col = i % cols;

      // 随机选择节点类型
      const nodeType = nodeTypes[Math.floor(Math.random() * nodeTypes.length)];

      // 计算位置
      let x, y;
      if (options.layout === "grid") {
        // 网格布局，添加小的随机偏移
        x = startX + col * cellWidth + (Math.random() - 0.5) * 10;
        y = startY + row * cellHeight + (Math.random() - 0.5) * 10;
      } else {
        // 随机布局
        const maxX = Math.max(2000, cols * cellWidth);
        const maxY = Math.max(1500, rows * cellHeight);
        x = startX + Math.random() * maxX;
        y = startY + Math.random() * maxY;
      }

      let nodeConfig: any = {
        shape: nodeType,
        x: x,
        y: y,
        width: 60,
        height: 40,
        label: `设备${i + 1}`,
        attrs: {
          body: {
            fill: "#EFF4FF",
            stroke: "#5F95FF",
            strokeWidth: 1,
          },
          text: {
            fontSize: 10,
            fill: "#262626",
          },
        },
      };

      // electrical 元件自动补 ports
      if (electricalPortsMap[nodeType]) {
        nodeConfig.ports = {
          groups: ports.groups,
          items: electricalPortsMap[nodeType],
        };
      }

      // 对于polygon类型，需要特殊处理
      if (nodeType === "polygon") {
        nodeConfig.attrs.body.refPoints =
          "0,10 10,0 20,0 30,10 30,20 20,30 10,30 0,20";
      }

      nodeConfigs.push(nodeConfig);
    }

    // 逐个添加节点（确保兼容性）
    for (let i = 0; i < nodeConfigs.length; i++) {
      try {
        const node = graph.value.addNode(nodeConfigs[i]);
        if (node) {
          nodes.push(node);
          nodeMap.set(i, node);
        }
      } catch (e) {
        console.error(`添加节点 ${i} 失败:`, e);
      }
    }

    // 生成稀疏且符合逻辑的连线
    const edges: any[] = [];
    const connectedPairs = new Set(); // 记录已连接的元件对，避免重复
    
    // 分类元件
    const powerSources = nodes.filter(node => ['voltage_source', 'current_source', 'generator'].includes(node.shape));
    const grounds = nodes.filter(node => node.shape === 'ground');
    const loads = nodes.filter(node => ['resistor', 'capacitor', 'inductor', 'motor'].includes(node.shape));
    const switches = nodes.filter(node => ['switch', 'fuse', 'relay'].includes(node.shape));
    const semiconductors = nodes.filter(node => ['diode', 'transistor', 'thyristor'].includes(node.shape));
    const transformers = nodes.filter(node => node.shape === 'transformer');
    
    // 只连接部分元件，保持稀疏性
    const shouldConnect = (nodeA: any, nodeB: any): boolean => {
      const distance = Math.sqrt(
        Math.pow(nodeA.position().x - nodeB.position().x, 2) + 
        Math.pow(nodeA.position().y - nodeB.position().y, 2)
      );
      // 只连接距离适中的元件（不要太远，也不要太近）
      return distance > 80 && distance < 200;
    };
    
    // 连接函数：在两个元件间建立连线
    const connectNodes = (nodeA: any, nodeB: any) => {
      const portsA = nodeA.ports?.items || [];
      const portsB = nodeB.ports?.items || [];
      
      if (portsA.length === 0 || portsB.length === 0) return false;
      
      const pairKey = [nodeA.id, nodeB.id].sort().join('|');
      if (connectedPairs.has(pairKey)) return false;
      
      // 随机选择端口
      const sourcePort = portsA[Math.floor(Math.random() * portsA.length)];
      const targetPort = portsB[Math.floor(Math.random() * portsB.length)];
      
      try {
        const edge = graph.value.addEdge({
          source: { cell: nodeA.id, port: sourcePort.id },
          target: { cell: nodeB.id, port: targetPort.id },
          attrs: {
            line: {
              stroke: '#5F95FF',
              strokeWidth: 2,
              targetMarker: null,
            },
          },
          connector: { name: 'normal' },
          router: { name: 'orth', args: { padding: 5 } },
        });
        
        if (edge) {
          edges.push(edge);
          connectedPairs.add(pairKey);
          return true;
        }
      } catch (e) {
        console.error('添加连线失败:', e);
      }
      return false;
    };
    
    // 1. 电源到负载的连接（最重要的连接）
    powerSources.forEach(source => {
      const nearbyLoads = loads.filter(load => shouldConnect(source, load));
      const connectionCount = Math.min(2, nearbyLoads.length); // 每个电源最多连2个负载
      
      for (let i = 0; i < connectionCount; i++) {
        const targetLoad = nearbyLoads[Math.floor(Math.random() * nearbyLoads.length)];
        connectNodes(source, targetLoad);
      }
    });
    
    // 2. 开关和保护元件的连接（串联在回路中）
    switches.forEach(switchNode => {
      const nearbyComponents = [...loads, ...semiconductors].filter(comp => shouldConnect(switchNode, comp));
      if (nearbyComponents.length > 0) {
        const target = nearbyComponents[Math.floor(Math.random() * nearbyComponents.length)];
        connectNodes(switchNode, target);
      }
    });
    
    // 3. 变压器的连接
    transformers.forEach(transformer => {
      const nearbyLoads = loads.filter(load => shouldConnect(transformer, load));
      if (nearbyLoads.length > 0) {
        const target = nearbyLoads[Math.floor(Math.random() * nearbyLoads.length)];
        connectNodes(transformer, target);
      }
    });
    
    // 4. 接地连接（接地应该连接到某些元件）
    grounds.forEach(ground => {
      const nearbyComponents = [...loads, ...semiconductors, ...powerSources].filter(comp => shouldConnect(ground, comp));
      if (nearbyComponents.length > 0) {
        const target = nearbyComponents[Math.floor(Math.random() * nearbyComponents.length)];
        connectNodes(ground, target);
      }
    });
    
    // 5. 确保所有元件连接成一个统一的网络
    
    // 构建连接图来检测连通分量
    const buildConnectionGraph = () => {
      const adjacencyList = new Map();
      
      // 初始化邻接表
      nodes.forEach(node => {
        adjacencyList.set(node.id, new Set());
      });
      
      // 添加边的连接关系
      edges.forEach(edge => {
        const source = edge.getSource();
        const target = edge.getTarget();
        if (source && source.cell && target && target.cell) {
          adjacencyList.get(source.cell)?.add(target.cell);
          adjacencyList.get(target.cell)?.add(source.cell);
        }
      });
      
      return adjacencyList;
    };
    
    // 使用DFS查找连通分量
    const findConnectedComponents = (adjacencyList: Map<string, Set<string>>) => {
      const visited = new Set();
      const components: string[][] = [];
      
      const dfs = (nodeId: string, component: string[]) => {
        visited.add(nodeId);
        component.push(nodeId);
        
        const neighbors = adjacencyList.get(nodeId) || new Set();
        neighbors.forEach(neighborId => {
          if (!visited.has(neighborId)) {
            dfs(neighborId, component);
          }
        });
      };
      
      nodes.forEach(node => {
        if (!visited.has(node.id)) {
          const component: string[] = [];
          dfs(node.id, component);
          components.push(component);
        }
      });
      
      return components;
    };
    
    // 连接不同的连通分量
    const connectComponents = () => {
      let adjacencyList = buildConnectionGraph();
      let components = findConnectedComponents(adjacencyList);
      
      // 如果只有一个连通分量，说明已经全部连通
      while (components.length > 1) {
        // 找到最大的连通分量作为主网络
        const mainComponent = components.reduce((max, current) => 
          current.length > max.length ? current : max
        );
        
        // 将其他分量连接到主网络
        const otherComponents = components.filter(comp => comp !== mainComponent);
        
        otherComponents.forEach(component => {
          // 找到两个分量之间距离最近的节点对
          let minDistance = Infinity;
          let bestMainNode: any = null;
          let bestComponentNode: any = null;
          
          const mainNodes = nodes.filter(node => mainComponent.includes(node.id));
          const componentNodes = nodes.filter(node => component.includes(node.id));
          
          mainNodes.forEach(mainNode => {
            componentNodes.forEach(componentNode => {
              const distance = Math.sqrt(
                Math.pow(mainNode.position().x - componentNode.position().x, 2) + 
                Math.pow(mainNode.position().y - componentNode.position().y, 2)
              );
              
              if (distance < minDistance) {
                minDistance = distance;
                bestMainNode = mainNode;
                bestComponentNode = componentNode;
              }
            });
          });
          
          // 连接最近的节点对
          if (bestMainNode && bestComponentNode) {
            connectNodes(bestMainNode, bestComponentNode);
          }
        });
        
        // 重新计算连通分量
        adjacencyList = buildConnectionGraph();
        components = findConnectedComponents(adjacencyList);
      }
    };
    
    // 执行连通性检查和修复
    connectComponents();
    
    // 6. 少量的额外连接（保持稀疏）
    const maxAdditionalConnections = Math.floor(nodes.length / 10); // 进一步减少额外连接
    let additionalConnections = 0;
    
    for (let i = 0; i < nodes.length && additionalConnections < maxAdditionalConnections; i++) {
      const nodeA = nodes[i];
      const nearbyNodes = nodes.filter((nodeB, idx) => 
        idx !== i && shouldConnect(nodeA, nodeB)
      );
      
      if (nearbyNodes.length > 0 && Math.random() < 0.2) { // 降低到20%的概率
        const target = nearbyNodes[Math.floor(Math.random() * nearbyNodes.length)];
        if (connectNodes(nodeA, target)) {
          additionalConnections++;
        }
      }
    }
    
    console.log(`已创建 ${edges.length} 条合理连线`);

    // 恢复画布功能
    if (options.count > 200) {
      graph.value.enableSelection?.();
      graph.value.enableKeyboard?.();
    }

    // 缩放以适应视图
    setTimeout(() => {
      graph.value.zoomToFit({
        padding: 50,
        maxScale: 1,
        minScale: 0.2,
      });
      graph.value.centerContent();
    }, 200);

    ElMessage.success(
      `已生成${nodes.length}个电力元件和${edges.length}条连接！`
    );

    // 性能提示
    if (nodes.length > 200) {
      const tips: string[] = [];
      if (nodes.length > 400) {
        tips.push("元件数量较多，建议缩小视图查看全局");
        tips.push("如需详细编辑，请使用框选功能选择局部区域");
      } else {
        tips.push("元件数量较多，如遇性能问题可适当缩小视图");
      }

      setTimeout(() => {
        tips.forEach((tip, index) => {
          setTimeout(() => {
            ElMessage.info(tip);
          }, index * 1500);
        });
      }, 1000);
    }
  } catch (error: any) {
      console.error("生成测试图失败:", error);
      ElMessage.error("生成测试图失败，请查看控制台错误信息");
    }
};

// 清空画布
const clearX6 = () => {
  x6Store.currentNodeChange(null);
  graph.value.clearCells();
};
// 删除当前选中组件
const deleteClick = () => {
  if (x6Store.currentNode) {
    x6Store.currentNode.remove();
    x6Store.currentNodeChange(null);
  } else {
    ElMessage({
      grouping: true,
      message: "当前未选中组件",
      type: "warning",
    });
  }
};

// 保存
const save = async () => {
  // 保存 JSON 数据'
  await nextTick();
  let imageData = "";
  let svgData = "";
  const jsonData = graph.value.toJSON();
  // x6Store.initJSONChange(jsonData);

  try {
    // 导出图片
    await graph.value.toPNG((dataUri: string) => {
      console.log("dataUri: ", dataUri);
      imageData = dataUri;
    });

     await graph.value.toSVG((data: string) => {
      console.log("dataSvg: ", data);
      svgData = data;

      
    });

    // 弹出对话框询问组件名称
    ElMessageBox.prompt("请输入组件名称", "保存自定义组件", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      inputPlaceholder: "自定义组件名称",
      inputValue: `自定义组件${x6Store.userComponents.length + 1}`,
      inputValidator: (value) => {
        if (!value) {
          return "组件名称不能为空";
        }
        return true;
      },
    })
      .then(({ value: componentName }) => {
        // 生成唯一ID
        const uniqueId = `svg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        try {
          // 新增：JSON数据方案（推荐使用）
          const useJsonMode = true; // 可以通过配置或用户选择来控制
          
          let savedComponent;
          
          if (useJsonMode) {
            // JSON方案：提取组件信息并保存
            const componentInfo = x6Store.extractComponentInfoFromGraph(jsonData, componentName);
            console.log('提取的组件信息:', componentInfo);
            
            // 生成JSON组件的统一ID
            const jsonComponentId = `json_${uniqueId}`;
            
            // 注册JSON组件
            const registrationResult = registerDynamicJsonComponent(jsonComponentId, componentInfo, {
              width: 120,
              height: 80,
              name: componentName
            });
            
            if (registrationResult) {
              savedComponent = x6Store.saveUserComponentAsJson(
                componentName,
                jsonData,
                componentInfo,
                jsonComponentId
              );
              
              if (savedComponent) {
                // 添加到动态组件列表（JSON方式）
                addDynamicComponent({
                  id: savedComponent.id,
                  name: componentName,
                  svgData: '', // JSON方式不需要SVG
                  jsonData: jsonData,
                  componentInfo: componentInfo,
                  renderType: 'json',
                  createdAt: Date.now()
                });
              }
            }
          } else {
            // 原有SVG方案（保留兼容性）
            // 1. 动态注册这个SVG组件，并获取处理后的数据
            const registrationResult = registerDynamicSvg(uniqueId, svgData, {
              width: 120,
              height: 80,
              name: componentName
            });

            // 2. 保存到用户组件存储 - 使用处理后的SVG数据
            savedComponent = x6Store.saveUserComponent(
              componentName,
              jsonData,
              registrationResult.processedSvgData, // 使用处理后的SVG数据
              uniqueId  // 传入唯一ID
            );
            
            if (savedComponent) {
              // 3. 添加到动态组件列表（会在"实现"标签页显示）- 使用处理后的SVG数据
              addDynamicComponent({
                id: uniqueId,
                name: componentName,
                svgData: registrationResult.processedSvgData, // 使用处理后的SVG数据
                jsonData: jsonData,
                renderType: 'svg',
                createdAt: Date.now()
              });
            }
          }

          if (savedComponent) {
            ElMessage({
              grouping: true,
              message: `组件「${componentName}」保存成功，可在「实现」标签页中使用！`,
              type: "success",
            });
          } else {
            ElMessage({
              grouping: true,
              message: `组件「${componentName}」保存失败`,
              type: "error",
            });
          }
        } catch (error: any) {
          console.error('动态注册组件失败:', error);
          ElMessage({
            grouping: true,
            message: `组件注册失败: ${error.message}`,
            type: "error",
          });
        }
      })
      .catch(() => {
        // 用户取消输入
        ElMessage({
          grouping: true,
          message: "取消保存",
          type: "info",
        });
      });
  } catch (error) {
    console.error("图片导出失败:", error);
    ElMessage({
      grouping: true,
      message: "图片导出失败",
      type: "error",
    });
  }
};

// 切换选中组件清空原组件工具
watch(
  () => x6Store.currentNode,
  (_newVal, oldVal) => {
    if (!oldVal) return;
    oldVal.removeTools();
  }
);

// 监听元件数量变化，自动调整连接数量
watch(
  () => generateForm.value.count,
  (newCount) => {
    // 自动调整连接数量为元件数量的1.5倍
    generateForm.value.connectionCount = Math.floor(newCount * 1.5);
  }
);

onMounted(() => {
  init();

  if (graph.value) {
    // 监听边的添加
    graph.value.on("edge:connected", ({ edge }: any) => {
      edge.attr({
        line: {
          strokeDasharray: "",
          style: {
            animation: "ant-line 30s infinite linear",
          },
        },
      });
    });

    // 边连接的动画效果
    const css = document.createElement("style");
    css.type = "text/css";
    css.innerHTML = `
      @keyframes ant-line {
        to {
          stroke-dashoffset: -1000;
        }
      }
    `;
    document.getElementsByTagName("head")[0].appendChild(css);
  }
  
  // 监听全局点击事件，隐藏右键菜单
  document.addEventListener('click', hideContextMenu);
});

onUnmounted(() => {
  graph.value.dispose();
  document.removeEventListener('click', hideContextMenu);
});
provide("graph", graph);

// 辅助函数：获取 cells 的整体包围盒
function getCellsBBox(cells: any[]) {
  const nodes = cells.filter((cell) => cell.shape && !cell.isEdge);
  if (nodes.length === 0) return { x: 0, y: 0, width: 0, height: 0 };
  let minX = Infinity,
    minY = Infinity,
    maxX = -Infinity,
    maxY = -Infinity;
  nodes.forEach((node) => {
    const x = node.position?.x ?? node.x ?? 0;
    const y = node.position?.y ?? node.y ?? 0;
    const width = node.size?.width ?? node.width ?? 0;
    const height = node.size?.height ?? node.height ?? 0;
    minX = Math.min(minX, x);
    minY = Math.min(minY, y);
    maxX = Math.max(maxX, x + width);
    maxY = Math.max(maxY, y + height);
  });
  return { x: minX, y: minY, width: maxX - minX, height: maxY - minY };
}

// 辅助函数：生成唯一ID
function generateId() {
  return "node_" + Math.random().toString(36).substr(2, 9) + "_" + Date.now();
}

// 右键菜单相关函数
const showNodeContextMenu = (node: any, e: MouseEvent) => {
  selectedNode.value = node;
  
  // 获取画布容器的位置
  const container = document.getElementById('container');
  if (!container) return;
  
  const rect = container.getBoundingClientRect();
  
  contextMenuPosition.value = {
    x: e.clientX - rect.left,
    y: e.clientY - rect.top
  };
  
  contextMenuVisible.value = true;
  
  // 确保菜单不会超出画布边界
  nextTick(() => {
    if (contextMenuRef.value) {
      const menuRect = contextMenuRef.value.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      
      // 调整水平位置
      if (contextMenuPosition.value.x + menuRect.width > containerRect.width) {
        contextMenuPosition.value.x = containerRect.width - menuRect.width - 10;
      }
      
      // 调整垂直位置
      if (contextMenuPosition.value.y + menuRect.height > containerRect.height) {
        contextMenuPosition.value.y = containerRect.height - menuRect.height - 10;
      }
    }
  });
};

const hideContextMenu = () => {
  contextMenuVisible.value = false;
  selectedNode.value = null;
};

// 层级操作函数
const bringToFront = () => {
  if (selectedNode.value) {
    selectedNode.value.toFront();
    ElMessage.success('已置于顶层');
  }
  hideContextMenu();
};

const sendToBack = () => {
  if (selectedNode.value) {
    selectedNode.value.toBack();
    ElMessage.success('已置于底层');
  }
  hideContextMenu();
};

const bringForward = () => {
  if (selectedNode.value) {
    const currentZIndex = selectedNode.value.getZIndex() || 0;
    selectedNode.value.setZIndex(currentZIndex + 1);
    ElMessage.success('已上移一层');
  }
  hideContextMenu();
};

const sendBackward = () => {
  if (selectedNode.value) {
    const currentZIndex = selectedNode.value.getZIndex() || 0;
    selectedNode.value.setZIndex(Math.max(0, currentZIndex - 1));
    ElMessage.success('已下移一层');
  }
  hideContextMenu();
};

const deleteSelectedNode = () => {
  if (selectedNode.value) {
    selectedNode.value.remove();
    ElMessage.success('节点已删除');
  }
  hideContextMenu();
};


</script>

<style scoped lang="scss">
article {
  overflow: hidden;
  user-select: none;
  height: 100vh;
  background: #f2f3f5;
  display: flex;
  flex-direction: column;

  .header {
    height: 64px;
    background: #fff;
    border-bottom: 1px solid #e5e6eb;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 10;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;
      .logo {
        width: 32px;
        height: 32px;
        transition: transform 0.3s ease;
        &:hover {
          transform: scale(1.1);
        }
      }
      .title {
        font-size: 20px;
        font-weight: 600;
        color: #1d2129;
        letter-spacing: 0.5px;
        transition: color 0.3s ease;

        &:hover {
          color: #165dff;
        }
      }
    }

    .header-right {
      display: flex;
      gap: 16px;
      :deep(.el-button) {
        font-size: 14px;
        font-weight: 500;
        border-radius: 6px;
        padding: 8px 20px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.02);

        &:not(.el-button--primary) {
          border: 1px solid #e5e6eb;
          background: #fff;

          &:hover {
            border-color: #165dff;
            color: #165dff;
            background: #f2f3ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(22, 93, 255, 0.1);
          }
        }

        &.el-button--primary {
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(22, 93, 255, 0.2);
          }
        }
      }
    }
  }

  .main {
    flex: 1;
    position: relative;
    display: flex;
    overflow: hidden;

    .left {
      width: 280px;
      height: 100%;
      background: #fff;
      border-right: 1px solid #e5e6eb;
      z-index: 2;
      transition: transform 0.3s ease;
    }

    .center {
      flex: 1;
      height: 100%;
      position: relative;
      z-index: 1;
      #container {
        width: 100%;
        height: 100%;
        background: #f2f3f5;
        background-image: linear-gradient(
            rgba(200, 200, 200, 0.1) 1px,
            transparent 1px
          ),
          linear-gradient(90deg, rgba(200, 200, 200, 0.1) 1px, transparent 1px);
        background-size: 20px 20px;
      }
    }

    .right {
      width: 320px;
      height: 100%;
      background: #fff;
      border-left: 1px solid #e5e6eb;
      overflow: hidden;
      z-index: 2;
    }
  }
}

// 右键菜单样式
.context-menu {
  position: absolute;
  background: #ffffff;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 160px;
  padding: 4px 0;
  font-size: 14px;
  
  .context-menu-item {
    padding: 8px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: #f5f6fa;
    }
    
    &.context-menu-item-danger {
      color: #f56565;
      
      &:hover {
        background-color: #fef2f2;
      }
    }
    
    i {
      width: 16px;
      font-size: 14px;
    }
    
    span {
      flex: 1;
    }
  }
  
  .context-menu-divider {
    height: 1px;
    background-color: #e5e6eb;
    margin: 4px 0;
  }
}
</style>
