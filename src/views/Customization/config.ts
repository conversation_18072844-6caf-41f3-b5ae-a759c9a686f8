// 连接桩配置
export const ports = {
  groups: {
    absolute: {
      position: 'absolute',
      attrs: {
        circle: {
          r: 2,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: 'rgba(95,149,255,0.2)', // 半透明蓝色
          style: {
            visibility: 'visible', // 平时可见
            cursor: 'crosshair',
          },
        },
      },
      markup: [
        {
          tagName: 'circle',
          selector: 'circle',
        },
      ],
    },
    top: {
      position: 'top',
      attrs: {
        circle: {
          r: 2,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: 'rgba(95,149,255,0.2)',
          style: {
            visibility: 'hidden',
          },
        },
      },
      markup: [
        {
          tagName: 'circle',
          selector: 'circle',
          attrs: {
            'port-group': 'top',
          },
        },
      ],
    },
    right: {
      position: 'right',
      attrs: {
        circle: {
          r: 2,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: 'rgba(95,149,255,0.2)',
          style: {
            visibility: 'hidden',
          },
        },
      },
      markup: [
        {
          tagName: 'circle',
          selector: 'circle',
          attrs: {
            'port-group': 'right',
          },
        },
      ],
    },
    bottom: {
      position: 'bottom',
      attrs: {
        circle: {
          r: 2,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: 'rgba(95,149,255,0.2)',
          style: {
            visibility: 'hidden',
          },
        },
      },
      markup: [
        {
          tagName: 'circle',
          selector: 'circle',
          attrs: {
            'port-group': 'bottom',
          },
        },
      ],
    },
    left: {
      position: 'left',
      attrs: {
        circle: {
          r: 2,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: 'rgba(95,149,255,0.2)',
          style: {
            visibility: 'hidden',
          },
        },
      },
      markup: [
        {
          tagName: 'circle',
          selector: 'circle',
          attrs: {
            'port-group': 'left',
          },
        },
      ],
    },
  },
  // 为了让自定义的可以生效，需要把items注释掉
  // items: [
  //   {
  //     id: 'top',
  //     group: 'top',
  //   },
  //   {
  //     id: 'right',
  //     group: 'right',
  //   },
  //   {
  //     id: 'bottom',
  //     group: 'bottom',
  //   },
  //   {
  //     id: 'left',
  //     group: 'left',
  //   },
  // ],
};

// 连线样式
export const connectLine = {
  connector: {
    name: 'normal',  // 使用直线连接器
  },
  router: {
    name: 'orth',  // 使用 orth 路由，更稳定
    args: {
      padding: 10,
    },
  },
  attrs: {
    line: {
      stroke: '#5F95FF',
      strokeWidth: 3,
      strokeLinecap: 'round',
      
    },
  },
};