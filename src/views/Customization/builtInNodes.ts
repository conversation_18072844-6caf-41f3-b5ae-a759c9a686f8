import { Graph } from '@antv/x6';
// import { ports } from './config';

// 扩展端口配置，确保包含 absolute 组
// const extendedPorts = {
//   ...ports,
//   groups: {
//     ...ports.groups,
//     absolute: {
//       position: 'absolute',
//       attrs: {
//         circle: {
//           r: 4,
//           magnet: true,
//           stroke: '#5F95FF',
//           strokeWidth: 1,
//           fill: '#fff',
//           style: {
//             visibility: 'visible',
//             cursor: 'crosshair',
//           },
//         },
//       },
//       markup: [
//         {
//           tagName: 'circle',
//           selector: 'circle',
//         },
//       ],
//     },
//   },
//   items: [
//     { id: 'top', group: 'top' },
//     { id: 'right', group: 'right' },
//     { id: 'bottom', group: 'bottom' },
//     { id: 'left', group: 'left' },
//   ],
// };
const extendedPorts = {}

// 电力元件颜色主题
const POWER_COLORS = {
  primary: '#5F95FF',
  danger: '#FF4D4F',
  warning: '#FAAD14',
  success: '#52C41A',
  info: '#1890FF',
  purple: '#722ED1',
  cyan: '#13C2C2',
  magenta: '#EB2F96',
};

// 注册内置矩形 - 基础电力元件
export const registerBuiltInRect = () => {
  Graph.registerNode(
    'power-rect',
    {
      inherit: 'rect',
      width: 100,
      height: 60,
      attrs: {
        body: {
          strokeWidth: 2,
          stroke: POWER_COLORS.primary,
          fill: '#EFF4FF',
          rx: 4,
          ry: 4,
        },
        text: {
          fontSize: 12,
          fill: '#262626',
        },
      },
      ports: { ...extendedPorts },
    },
    true
  );
};

// 注册内置圆形 - 发电机/电动机
export const registerBuiltInCircle = () => {
  Graph.registerNode(
    'power-circle',
    {
      inherit: 'circle',
      width: 80,
      height: 80,
      attrs: {
        body: {
          strokeWidth: 2,
          stroke: POWER_COLORS.success,
          fill: '#F6FFED',
        },
        text: {
          fontSize: 12,
          fill: '#262626',
        },
      },
      ports: { ...extendedPorts },
    },
    true
  );
};

// 注册内置椭圆 - 电容器
export const registerBuiltInEllipse = () => {
  Graph.registerNode(
    'power-ellipse',
    {
      inherit: 'ellipse',
      width: 120,
      height: 60,
      attrs: {
        body: {
          strokeWidth: 2,
          stroke: POWER_COLORS.warning,
          fill: '#FFFBE6',
        },
        text: {
          fontSize: 12,
          fill: '#262626',
        },
      },
      ports: { ...extendedPorts },
    },
    true
  );
};

// 多边形元件已删除 - 不符合电力元件特性

// 注册内置菱形 - 开关/断路器
export const registerBuiltInDiamond = () => {
  Graph.registerNode(
    'power-diamond',
    {
      inherit: 'polygon',
      width: 100,
      height: 100,
      attrs: {
        body: {
          strokeWidth: 2,
          stroke: POWER_COLORS.danger,
          fill: '#FFF1F0',
          refPoints: '50,0 100,50 50,100 0,50', // 菱形
        },
        text: {
          fontSize: 12,
          fill: '#262626',
        },
      },
      ports: { ...extendedPorts },
    },
    true
  );
};

// 注册内置三角形 - 接地符号
export const registerBuiltInTriangle = () => {
  Graph.registerNode(
    'power-triangle',
    {
      inherit: 'polygon',
      width: 80,
      height: 80,
      attrs: {
        body: {
          strokeWidth: 2,
          stroke: POWER_COLORS.info,
          fill: '#E6F7FF',
          refPoints: '50,0 100,100 0,100', // 三角形
        },
        text: {
          fontSize: 12,
          fill: '#262626',
          refY: '70%',
        },
      },
      ports: { ...extendedPorts },
    },
    true
  );
};

// 圆柱元件已删除 - 不符合电力元件特性

// 图标矩形已删除 - 不符合电力元件特性

// 状态矩形已删除 - 不符合电力元件特性

// 注册引脚节点（现在使用Vue组件方式，此函数保留为空以兼容）
export const registerCustomPin = () => {
  // 引脚现在通过Vue组件在register.ts中注册
};

// 注册所有内置节点
export const registerAllBuiltInNodes = () => {
  registerBuiltInRect();
  registerBuiltInCircle();
  registerBuiltInEllipse();
  registerBuiltInDiamond();
  registerBuiltInTriangle();
  registerCustomPin(); // 添加引脚注册
};

// 导出节点配置列表
export const builtInNodeList = [
  {
    key: 'power-rect',
    name: '矩形元件',
    category: '基础元件',
    description: '通用矩形电力元件',
  },
  {
    key: 'power-circle',
    name: '圆形元件',
    category: '旋转设备',
    description: '发电机、电动机等旋转设备',
  },
  {
    key: 'power-ellipse',
    name: '椭圆元件',
    category: '储能设备',
    description: '电容器等储能设备',
  },
  {
    key: 'power-diamond',
    name: '菱形元件',
    category: '开关设备',
    description: '开关、断路器等控制设备',
  },
  {
    key: 'power-triangle',
    name: '三角形元件',
    category: '接地保护',
    description: '接地符号、保护装置',
  },
  // {
  //   key: 'custom-pin',
  //   name: '自定义引脚',
  //   category: '引脚元件',
  //   description: '可拖拽的自定义引脚',
  // },
]; 