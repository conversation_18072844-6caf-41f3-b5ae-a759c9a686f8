<template>
  <div class="box">
    <div class="tabs">
      <p
        v-for="(item, index) in tabsList"
        :key="index"
        :class="{ active: tabsActive === item }"
        @click="tabsClick(item)"
      >
        {{ item }}
      </p>
    </div>
    <div class="content">
      <div v-if="appStore.currentNode?.id">
        <div v-show="tabsActive === '属性'">
          <p>
            <span>宽度：</span>
            <el-input-number
              v-model="widthValue"
              :controls="false"
              :min="0"
              @change="widthChange"
            />
          </p>
          <p>
            <span>高度：</span>
            <el-input-number
              v-model="heightValue"
              :controls="false"
              :min="0"
              @change="heightChange"
            />
          </p>
        </div>
        
        <div v-show="tabsActive === '参数'">
          <div class="section">
            <h4>三相电组</h4>
            <p>
              <span>是否参数导出：</span>
              <el-switch v-model="isParamExport" />
              <span class="switch-label">{{ isParamExport ? '是' : '否' }}</span>
            </p>
          </div>
          
          <div class="section">
            <p>
              <span>自定义名称：</span>
              <el-input v-model="customName" placeholder="LED激流电组" />
            </p>
            <p>
              <span>A相电阻值：</span>
              <el-input-number v-model="aPhaseResistance" :controls="false" :min="0" />
            </p>
            <p>
              <span>B相电阻值：</span>
              <el-input-number v-model="bPhaseResistance" :controls="false" :min="0" />
            </p>
            <p>
              <span>C相电阻值：</span>
              <el-input-number v-model="cPhaseResistance" :controls="false" :min="0" />
            </p>
            <p>
              <span>A相电阻电流：</span>
              <el-input v-model="aPhaseCurrent" placeholder="Ir" />
            </p>
            <p>
              <span>A相电阻电压：</span>
              <el-input v-model="aPhaseVoltage" placeholder="Vr" />
            </p>
            <p>
              <span>B相电阻电流：</span>
              <el-input v-model="bPhaseCurrent" placeholder="Ir" />
            </p>
            <p>
              <span>B相电阻电压：</span>
              <el-input v-model="bPhaseVoltage" placeholder="Vr" />
            </p>
            <p>
              <span>C相电阻电流：</span>
              <el-input v-model="cPhaseCurrent" placeholder="Ir" />
            </p>
            <p>
              <span>C相电阻电压：</span>
              <el-input v-model="cPhaseVoltage" placeholder="Vr" />
            </p>
          </div>
        </div>
        
        <div v-show="tabsActive === '引脚'">
          <div class="section">
            <p>
              <span>输出引脚A：</span>
              <el-input v-model="outputPinA" placeholder="请选择对应连接..." readonly />
            </p>
            <p>
              <span>输出引脚B：</span>
              <el-input v-model="outputPinB" placeholder="请选择对应连接..." readonly />
            </p>
            <p>
              <span>输出引脚C：</span>
              <el-input v-model="outputPinC" placeholder="请选择对应连接..." readonly />
            </p>
          </div>
        </div>
      </div>
      <div v-else>
        <img :src="unselected" alt="" />
      </div>
    </div>
    <div class="footer">
      当前选择：{{ appStore.currentNode?.id.substring(0, 8) }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import unselected from "@/assets/images/unselected.png";
import { useX6Store } from "@/store";

const appStore = useX6Store();

// tabs
const tabsList = ref(["属性", "参数", "引脚"]);
const tabsActive = ref("属性");
const tabsClick = (item: string) => {
  tabsActive.value = item;
};

// 属性
const widthValue = ref(0);
const heightValue = ref(0);

// 参数
const isParamExport = ref(true);
const customName = ref('LED激流电组');
const aPhaseResistance = ref(100);
const bPhaseResistance = ref(100);
const cPhaseResistance = ref(100);
const aPhaseCurrent = ref('Ir');
const aPhaseVoltage = ref('Vr');
const bPhaseCurrent = ref('Ir');
const bPhaseVoltage = ref('Vr');
const cPhaseCurrent = ref('Ir');
const cPhaseVoltage = ref('Vr');

// 引脚
const outputPinA = ref('请选择对应连接...');
const outputPinB = ref('请选择对应连接...');
const outputPinC = ref('请选择对应连接...');
watch(
  () => appStore.currentNode?.data,
  (value) => {
    if (value) {
      widthValue.value = value.width;
      heightValue.value = value.height;
    }
  }
);

const widthChange = (value?: number, _prev?: number) => {
  if (value) {
    appStore.currentNode.setData({
      ...appStore.currentNode.data,
      width: value,
    });
  }
};
const heightChange = (value?: number, _prev?: number) => {
  if (value) {
    appStore.currentNode.setData({
      ...appStore.currentNode.data,
      height: value,
    });
  }
};
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
 
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  
  .tabs {
    padding: 16px 16px 0;
    display: flex;
    gap: 12px;
    
    p {
      cursor: pointer;
      margin: 0;
      flex: 1;
      height: 40px;
      background: rgba(255, 255, 255, 0.04);
      border: 1px solid rgba(255, 255, 255, 0.08);
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(25, 118, 210, 0.08);
        border-color: rgba(25, 118, 210, 0.5);
      }
      
      &.active {
        background: rgba(25, 118, 210, 0.12);
        border-color: #1976d2;
        color: #1976d2;
      }
    }
  }
  
  .content {
    flex: 1;
    padding: 20px 16px;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
      width: 4px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.02);
      border-radius: 2px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 2px;
      
      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
    
    .section {
      margin-bottom: 24px;
      
      h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #1976d2;
        border-bottom: 2px solid #1976d2;
        padding-bottom: 8px;
      }
    }
    
    p {
      margin: 0 0 16px 0;
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 0;
      border-bottom: 1px solid #e0e0e0;
      
      span {
        display: block;
        width: auto;
        font-size: 14px;
        
        &.switch-label {
          margin-left: 8px;
          font-size: 12px;
          color: #666;
        }
      }
      
      :deep(.el-input-number),
      :deep(.el-input) {
        flex: 1;
        
        .el-input__wrapper {
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          box-shadow: none;
          transition: all 0.3s ease;
          background-color: #ffffff;
          
          &:hover {
            border-color: rgba(25, 118, 210, 0.5);
          }
          
          &.is-focus {
            border-color: #409eff;
            background: #ffffff;
          }
        }
        
        input {
          font-size: 14px;
          text-align: left;
          height: 32px;
          padding: 0 8px;
        }
      }
      
      :deep(.el-switch) {
        margin-right: 8px;
        
        .el-switch__core {
          background-color: #dcdfe6;
          
          &.is-checked {
            background-color: #1976d2;
          }
        }
      }
      
      :deep(.el-input-number) {
        
        .el-input-number__decrease,
        .el-input-number__increase {
          background: transparent;
          border-color: rgba(255, 255, 255, 0.08);
        
          
          &:hover {
            color: #1976d2;
          }
        }
      }
    }
    
    img {
      display: block;
      max-width: 100%;
      margin: 20px auto;
      opacity: 0.6;
    }
  }
  
  .footer {
    height: 40px;
    background: rgba(255, 255, 255, 0.04);
    border-top: 1px solid rgba(255, 255, 255, 0.08);
    display: flex;
    align-items: center;
    padding: 0 16px;
    font-size: 13px;
  
  }
}
</style>
