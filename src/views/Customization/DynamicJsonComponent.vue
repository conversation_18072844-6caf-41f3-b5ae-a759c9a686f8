<template>
  <div 
    class="dynamic-json-component"
    :style="{
      width: width + 'px',
      height: height + 'px',
      position: 'relative',
      border: '2px solid #4A90E2',
      borderRadius: '8px',
      backgroundColor: '#f8f9fa',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }"
    @mousedown="handleMouseDown"
    @mouseup="handleMouseUp"
    @mousemove="handleMouseMove"
  >
    <!-- 组件主体 -->
    <div class="component-body">
      <div class="component-name">{{ componentData?.name || 'JSON组件' }}</div>
    </div>
    
    <!-- 动态渲染引脚 -->
    <div 
      v-for="pin in pins" 
      :key="pin.id"
      class="pin"
      :style="{
        position: 'absolute',
        left: pin.x + 'px',
        top: pin.y + 'px',
        width: '8px',
        height: '8px',
        borderRadius: '50%',
        backgroundColor: pin.type === 'input' ? '#28a745' : '#dc3545',
        border: '2px solid #fff',
        boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
        zIndex: 10
      }"
      :title="pin.label || pin.id"
    >
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import type { ComponentInfo, ComponentPin } from '@/store/modules/useX6';

interface Props {
  node?: any;
  componentData?: ComponentInfo;
}

const props = withDefaults(defineProps<Props>(), {
  componentData: undefined
});

const width = ref(120);
const height = ref(80);

// 计算引脚位置
const pins = computed(() => {
  if (!props.componentData?.pins) return [];
  
  return props.componentData.pins.map((pin: ComponentPin) => {
    // 将相对位置转换为绝对位置
    const x = (pin.position.x / 100) * width.value - 4; // 减去引脚半径
    const y = (pin.position.y / 100) * height.value - 4;
    
    return {
      ...pin,
      x: Math.max(0, Math.min(x, width.value - 8)),
      y: Math.max(0, Math.min(y, height.value - 8))
    };
  });
});

// 监听节点数据变化
watch(() => props.node, (newNode) => {
  if (newNode) {
    const size = newNode.getSize();
    width.value = size.width;
    height.value = size.height;
  }
}, { immediate: true });

// 鼠标事件处理
const handleMouseDown = (event: MouseEvent) => {
  // 处理鼠标按下事件
};

const handleMouseUp = (event: MouseEvent) => {
  // 处理鼠标释放事件
};

const handleMouseMove = (event: MouseEvent) => {
  // 处理鼠标移动事件
};

onMounted(() => {
  console.log('DynamicJsonComponent mounted with data:', props.componentData);
});
</script>

<style scoped>
.dynamic-json-component {
  cursor: pointer;
  user-select: none;
}

.component-body {
  text-align: center;
  font-size: 12px;
  color: #333;
  pointer-events: none;
}

.component-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.pin {
  cursor: crosshair;
  transition: transform 0.2s;
}

.pin:hover {
  transform: scale(1.2);
}
</style>