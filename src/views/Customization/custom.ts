// 导入基础组件
import jcContainer from "../CustomComponent/jc/container.vue";
import jcLine from "../CustomComponent/jc/line.vue";
import jcPath from "../CustomComponent/jc/path.vue";
import jcText from "../CustomComponent/jc/text.vue";

import jcElectrical from "../CustomComponent/jc/electrical.vue";
import DynamicSvg from "../CustomComponent/jc/DynamicSvg.vue";
import DynamicJsonComponent from "../CustomComponent/jc/DynamicJsonComponent.vue";

// 导入仪表盘组件
import ybp1 from "../CustomComponent/ybp/ybp1.vue";
import ybp2 from "../CustomComponent/ybp/ybp2.vue";
import ybp3 from "../CustomComponent/ybp/ybp3.vue";
import ybp4 from "../CustomComponent/ybp/ybp4.vue";


export const jcCustom = {
  jcContainer,
  jcLine,
  jcPath,
  jcText,

  // 电气元件
  jcThyristor: jcElectrical,
  jcDiode: jcElectrical,
  jcTransistor: jcElectrical,
  jcResistor: jcElectrical,
  jcCapacitor: jcElectrical,
  jcInductor: jcElectrical,
  jcTransformer: jcElectrical,
  jcSwitch: jcElectrical,
  jcGround: jcElectrical,
  jcVoltageSource: jcElectrical,
  jcCurrentSource: jcElectrical,
  jcMotor: jcElectrical,
  jcGenerator: jcElectrical,
  jcFuse: jcElectrical,
  jcRelay: jcElectrical,
  
  // 自定义引脚
  jcCustomPin: jcElectrical,
  
  // 动态SVG组件
  dynamicSvg: DynamicSvg,
  // 动态JSON组件
  dynamicJsonComponent: DynamicJsonComponent,
};

export const ybpCustom = {
  ybp1,
  ybp2,
  ybp3,
  ybp4,
};

// 动态添加组件的函数
export const addDynamicComponent = (componentId: string, renderType: 'svg' | 'json' = 'svg') => {
  if (renderType === 'json') {
    (jcCustom as any)[componentId] = DynamicJsonComponent;
  } else {
    (jcCustom as any)[componentId] = DynamicSvg;
  }
};

// 新增：注册JSON组件的函数
export const registerDynamicJsonComponent = (componentId: string, componentInfo: any) => {
  (jcCustom as any)[componentId] = DynamicJsonComponent;
  console.log(`注册JSON组件: ${componentId}`, componentInfo);
};

