import { Graph, Node } from '@antv/x6';

export class DragPortManager {
  private graph: Graph;

  constructor(graph: Graph) {
    this.graph = graph;
    this.setupEventListeners();
  }

  private setupEventListeners() {
    // 监听节点添加事件
    this.graph.on('node:added', ({ node }) => {
      console.log('节点添加:', node.shape, node);
      // 如果是引脚组件被添加
      if (node.shape === 'custom-port' || node.shape === 'custom-port-v2') {
        console.log('检测到引脚组件被添加');
        // 延迟处理，确保节点已经完全添加到画布
        setTimeout(() => {
          this.handlePortDropped(node);
        }, 100);
      }
    });

    // 监听节点嵌入事件
    this.graph.on('node:embedded', ({ node, currentParent }) => {
      console.log('节点嵌入:', node.shape, currentParent);
      if ((node.shape === 'custom-port' || node.shape === 'custom-port-v2') && currentParent) {
        this.attachPortToNode(node, currentParent);
      }
    });
  }

  private handlePortDropped(portNode: Node) {
    const position = portNode.getPosition();
    const nodes = this.graph.getNodes();

    console.log('处理引脚放置，位置:', position);
    console.log('画布上的节点数:', nodes.length);

    // 查找引脚下方的节点
    let targetNode: Node | null = null;
    let minDistance = Infinity;

    for (const node of nodes) {
      if (node.id === portNode.id || node.shape === 'custom-port' || node.shape === 'custom-port-v2') continue;

      // const bbox = node.getBBox();
      const distance = this.getDistanceToNode(position, node);

      console.log(`节点 ${node.shape} 距离:`, distance);

      // 检查点是否在节点内部或边缘附近
      if (distance < minDistance && distance < 50) {
        minDistance = distance;
        targetNode = node;
      }
    }

    if (targetNode) {
      console.log('找到目标节点:', targetNode.shape);
      this.attachPortToNode(portNode, targetNode);
    } else {
      console.log('未找到目标节点，删除引脚');
      // 如果没有找到目标节点，删除引脚
      portNode.remove();
    }
  }

  private getDistanceToNode(point: { x: number; y: number }, node: Node): number {
    const bbox = node.getBBox();
    const shape = node.shape;

    // 对于圆形节点
    if (shape === 'circle' || shape === 'power-circle') {
      const centerX = bbox.x + bbox.width / 2;
      const centerY = bbox.y + bbox.height / 2;
      const radius = bbox.width / 2;
      const distance = Math.sqrt(Math.pow(point.x - centerX, 2) + Math.pow(point.y - centerY, 2));
      return Math.abs(distance - radius);
    }

    // 对于椭圆节点
    if (shape === 'ellipse' || shape === 'power-ellipse') {
      const centerX = bbox.x + bbox.width / 2;
      const centerY = bbox.y + bbox.height / 2;
      const a = bbox.width / 2;
      const b = bbox.height / 2;

      // 计算点到椭圆边缘的近似距离
      const dx = point.x - centerX;
      const dy = point.y - centerY;
      const angle = Math.atan2(dy, dx);
      const edgeX = centerX + a * Math.cos(angle);
      const edgeY = centerY + b * Math.sin(angle);

      return Math.sqrt(Math.pow(point.x - edgeX, 2) + Math.pow(point.y - edgeY, 2));
    }

    // 对于多边形节点（包括菱形、三角形等）
    if (shape === 'polygon' || shape === 'power-diamond' || shape === 'power-triangle') {
      // 简化处理：计算到中心的距离
      const centerX = bbox.x + bbox.width / 2;
      const centerY = bbox.y + bbox.height / 2;
      const avgRadius = (bbox.width + bbox.height) / 4;
      const distance = Math.sqrt(Math.pow(point.x - centerX, 2) + Math.pow(point.y - centerY, 2));
      return Math.abs(distance - avgRadius);
    }

    // 对于矩形和其他形状
    const left = bbox.x;
    const right = bbox.x + bbox.width;
    const top = bbox.y;
    const bottom = bbox.y + bbox.height;

    // 计算到四条边的距离
    const distances = [
      Math.abs(point.y - top),     // 到顶边
      Math.abs(point.x - right),   // 到右边
      Math.abs(point.y - bottom),  // 到底边
      Math.abs(point.x - left),    // 到左边
    ];

    // 如果点在矩形内部
    if (point.x >= left && point.x <= right && point.y >= top && point.y <= bottom) {
      return Math.min(...distances);
    }

    // 如果点在矩形外部，计算到最近角或边的距离
    let minDist = Infinity;

    // 检查四个角
    const corners = [
      { x: left, y: top },
      { x: right, y: top },
      { x: right, y: bottom },
      { x: left, y: bottom },
    ];

    for (const corner of corners) {
      const dist = Math.sqrt(Math.pow(point.x - corner.x, 2) + Math.pow(point.y - corner.y, 2));
      minDist = Math.min(minDist, dist);
    }

    return minDist;
  }

  private attachPortToNode(portNode: Node, targetNode: Node) {
    const portPos = portNode.getPosition();
    // const targetBBox = targetNode.getBBox();

    // 计算相对位置
    const relativePos = this.calculateRelativePosition(portPos, targetNode);

    // 创建端口
    const port = {
      id: `port-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      group: 'absolute',
      args: {
        position: relativePos,
      },
      markup: [
        {
          tagName: 'circle',
          selector: 'circle',
        },
      ],
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: '#fff',
          style: {
            visibility: 'visible',
            cursor: 'crosshair',
          },
        },
      },
    };

    // 添加端口到目标节点
    targetNode.addPort(port);

    // 删除引脚节点
    portNode.remove();

    // 触发端口添加事件
    this.graph.trigger('port:added', { node: targetNode, port });
  }

  private calculateRelativePosition(
    portPos: { x: number; y: number },
    targetNode: Node
  ): { x: number | string; y: number | string } {
    const bbox = targetNode.getBBox();
    const shape = targetNode.shape;

    // 对于圆形
    if (shape === 'circle' || shape === 'power-circle') {
      const centerX = bbox.x + bbox.width / 2;
      const centerY = bbox.y + bbox.height / 2;
      const radius = bbox.width / 2;

      // 计算角度
      const angle = Math.atan2(portPos.y - centerY, portPos.x - centerX);

      // 计算边缘位置
      const x = radius + radius * Math.cos(angle);
      const y = radius + radius * Math.sin(angle);

      return { x, y };
    }

    // 对于椭圆
    if (shape === 'ellipse' || shape === 'power-ellipse') {
      const centerX = bbox.x + bbox.width / 2;
      const centerY = bbox.y + bbox.height / 2;
      const a = bbox.width / 2;
      const b = bbox.height / 2;

      // 计算角度
      const angle = Math.atan2(portPos.y - centerY, portPos.x - centerX);

      // 计算椭圆边缘位置
      const x = a + a * Math.cos(angle);
      const y = b + b * Math.sin(angle);

      return { x, y };
    }

    // 对于其他形状，使用边缘吸附
    const relativeX = portPos.x - bbox.x;
    const relativeY = portPos.y - bbox.y;

    return this.snapToEdge(relativeX, relativeY, bbox.width, bbox.height);
  }

  private snapToEdge(x: number, y: number, width: number, height: number) {
    // const threshold = 20;

    // 检查距离哪条边最近
    const distances = [
      { edge: 'top', distance: y, x: x, y: 0 },
      { edge: 'right', distance: width - x, x: width, y: y },
      { edge: 'bottom', distance: height - y, x: x, y: height },
      { edge: 'left', distance: x, x: 0, y: y },
    ];

    // 找到最近的边
    const nearest = distances.reduce((min, curr) =>
      curr.distance < min.distance ? curr : min
    );

    // 限制在边缘范围内
    let finalX = nearest.x;
    let finalY = nearest.y;

    if (nearest.edge === 'top' || nearest.edge === 'bottom') {
      finalX = Math.max(0, Math.min(width, finalX));
    } else {
      finalY = Math.max(0, Math.min(height, finalY));
    }

    return { x: finalX, y: finalY };
  }
} 