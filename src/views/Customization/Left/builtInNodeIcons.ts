// 使用SVG字符串作为图标
export const builtInNodeIcons = {
  'power-rect': `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="3" y="6" width="18" height="12" stroke="currentColor" stroke-width="2" rx="2"/>
  </svg>`,
  
  'power-circle': `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
    <text x="12" y="16" text-anchor="middle" font-size="10" fill="currentColor">G</text>
  </svg>`,
  
  'power-ellipse': `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <ellipse cx="12" cy="12" rx="10" ry="6" stroke="currentColor" stroke-width="2"/>
  </svg>`,
  
  'power-diamond': `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 3 L21 12 L12 21 L3 12 Z" stroke="currentColor" stroke-width="2" fill="none"/>
  </svg>`,
  
  'power-triangle': `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 4 L20 18 L4 18 Z" stroke="currentColor" stroke-width="2" fill="none"/>
    <line x1="12" y1="18" x2="12" y2="22" stroke="currentColor" stroke-width="2"/>
  </svg>`,
  
  'custom-pin': `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="12" cy="12" r="6" fill="none" stroke="currentColor" stroke-width="2"/>
    <circle cx="12" cy="12" r="2" fill="currentColor"/>
  </svg>`,
  
  'custom-port': `<svg viewBox="0 0 50 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="8" cy="12" r="6" stroke="currentColor" stroke-width="2" fill="currentColor"/>
    <circle cx="8" cy="12" r="2" fill="white"/>
    <text x="22" y="16" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="currentColor">PIN</text>
  </svg>`,
};

// 将SVG字符串转换为Data URL
export const getIconDataUrl = (svgString: string, color: string = '#5F95FF'): string => {
  try {
    const coloredSvg = svgString.replace(/currentColor/g, color);
    // 使用base64编码，避免encodeURIComponent可能出现的问题
    const base64 = btoa(unescape(encodeURIComponent(coloredSvg)));
    return `data:image/svg+xml;base64,${base64}`;
  } catch (error) {
    console.warn('SVG编码失败，使用默认图标:', error);
    // 返回一个简单的默认SVG
    const defaultSvg = `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="3" y="3" width="18" height="18" stroke="${color}" stroke-width="2" fill="none"/></svg>`;
    const base64 = btoa(unescape(encodeURIComponent(defaultSvg)));
    return `data:image/svg+xml;base64,${base64}`;
  }
}; 