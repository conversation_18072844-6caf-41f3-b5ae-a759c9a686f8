<template>
  <div class="box">
    <div class="left">
      <el-tooltip
        v-for="(item, index) in imgList"
        :key="index"
        effect="light"
        :content="item.nickname"
        :hide-after="0"
        :show-arrow="false"
        :offset="-10"
        placement="left"
      >
        <p
          :class="{ active: imgActive === item.name }"
          @click="imgClick(item.name)"
        >
          <img :src="item.img" alt="" draggable="false" /></p
      ></el-tooltip>
    </div>
    <div class="right">
      <div class="supplies" ref="supplies">
        <div class="noEgImg" v-show="imgActive === 'jcIcon'">
          <li
            v-for="(item, index) in currentList"
            :key="index"
            :data-type="item.key"
            @mousedown="startDrag"
          >
            <div>
              <ElectricalIcon v-if="isElectricalComponent(item.key)" :type="item.key" :size="36" />
              <div v-else-if="item.svgData" v-html="item.svgData" class="svg-icon"></div>
              <img v-else :src="item.img" alt="" draggable="false" @error="handleImageError" />
            </div>
            <span>{{ item.name }}</span>
          </li>
        </div>
        <div class="egImg" v-show="imgActive === 'ybpIcon'">
          <div
            v-for="(item, index) in currentList"
            :key="index"
            :data-type="item.key"
            @mousedown="startDrag"
            class="eg-item"
          >
            <div v-html="item.svgData" class="svg-icon"></div>
            <span>{{ item.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, ref, watch, watchEffect, onMounted } from "vue";
import { Dnd } from "@antv/x6-plugin-dnd";

import { jcIcon, ybpIcon } from "./iconImg";
import { jcList, ybpList, getFullJcList, getDynamicUpdateCounter } from "./customImg";
import { ports } from '../config';
import { useX6Store } from "@/store";
import ElectricalIcon from "@/views/CustomComponent/jc/ElectricalIcon.vue";
import { registerDynamicSvg, registerDynamicJsonComponent } from "../register";
import type { UserComponent } from "@/store/modules/useX6";

// 左侧菜单
const imgActive = ref("jcIcon");
const imgList = ref([
  {
    name: "jcIcon",
    nickname: "元件接口",
    img: jcIcon,
  },
  {
    name: "ybpIcon",
    nickname: "实现",
    img: ybpIcon,
  }
]);
const jcJSON = ref(null);
const ybpJSON = ref(null);

const imgClick = (name: string) => {
  // 如果点击的是同一个按钮，不做任何操作
  if (imgActive.value === name) {
    return;
  }

  // 缓存当前画布
  if (imgActive.value === 'jcIcon') {
    jcJSON.value = graph.value.toJSON();
  } else if (imgActive.value === 'ybpIcon') {
    ybpJSON.value = graph.value.toJSON();
  }

  // 切换到新标签
  imgActive.value = name;

  // 还原目标画布
  if (name === 'jcIcon' && jcJSON.value) {
    graph.value.fromJSON(jcJSON.value);
    initDnd(); // 重新初始化Dnd
  } else if (name === 'ybpIcon' && ybpJSON.value) {
    graph.value.fromJSON(ybpJSON.value);
    initDnd(); // 重新初始化Dnd
  } else {
    graph.value.clearCells();
    initDnd(); // 重新初始化Dnd
  }
};

// 组件列表
const currentList = ref<any>([]);
const x6Store = useX6Store();

const currentListChange = (value: string) => {
  switch (value) {
    case "jcIcon":
      // 元件接口标签页：保留原有内置组件（用于创建自定义元件）
      currentList.value = jcList;
      return;
    case "ybpIcon":
      // 实现标签页：显示用户创建的自定义组件
      const dynamicSvgComponents = getFullJcList().filter(item => item.isDynamic);
      
      // 只显示非重复的用户组件（排除已经在动态组件中的）
      const userComponentsList = x6Store.userComponents
        .filter(comp => !dynamicSvgComponents.find(dynamic => dynamic.key === comp.id))
        .map(comp => ({
          key: comp.id,
          name: comp.name,
          svgData: comp.svgData,
          isUserComponent: true,
          jsonData: comp.jsonData
        }));
      
      currentList.value = [...dynamicSvgComponents, ...userComponentsList];
      return;
    default:
      currentList.value = [];
      return;
  }
};
watch(
  () => imgActive.value,
  (value) => {
    currentListChange(value);
  },
  { immediate: true }
);

// 监听用户组件变化，当在实现标签页时更新列表
watch(
  () => x6Store.userComponents,
  () => {
    if (imgActive.value === 'ybpIcon') {
      currentListChange('ybpIcon');
    }
  },
  { deep: true }
);

// 监听动态组件变化，当在实现标签页时更新列表
watch(
  () => getDynamicUpdateCounter(),
  () => {
    if (imgActive.value === 'ybpIcon') {
      currentListChange('ybpIcon');
    }
  }
);

// 拖拉拽
const graph: any = inject("graph");
const dnd = ref<any>(null);

const initDnd = () => {
  // 如果已经初始化过，先销毁旧的实例
  if (dnd.value) {
    dnd.value = null;
    console.log('Dnd instance cleared.');
  }
  
  if (!graph.value) return;
  
  dnd.value = new Dnd({
    target: graph.value,
    validateNode() {
      return true;
    },
  });
  console.log('Dnd instance re-initialized.');
};

const startDrag = (e: any) => {
  if (!graph.value || !dnd.value) return;
  const target = e.currentTarget;
  const type = target.getAttribute("data-type");
 
  // 查找是否是动态SVG组件
  const dynamicComponent = currentList.value.find((item: any) => 
    item.key === type && item.isDynamic
  );

  if (dynamicComponent) {
    // 创建动态SVG组件 - 需要传递 svgData
    let nodeConfig: any = { 
      shape: type,
      data: {
        svgData: dynamicComponent.svgData,
        componentName: dynamicComponent.name
      }
    };
    if (type !== 'custom-port' && type !== 'custom-port-v2') {
      nodeConfig.ports = ports;
    }
    
    let node = graph.value.createNode(nodeConfig);
    dnd.value.start(node, e);
    return;
  }

  // 查找是否是用户自定义组件
  const userComponent = currentList.value.find((item: any) => 
    item.key === type && item.isUserComponent
  );

  if (userComponent) {
    console.log('拖拽用户组件:', userComponent);
    
    // 检查是否是JSON组合组件
    if (userComponent.renderType === 'json' && userComponent.componentInfo) {
      console.log('创建JSON组合组件:', userComponent.componentInfo);
      
      // 创建JSON组合组件：使用Group节点
      const groupNode = graph.value.createNode({
        shape: type,  // 使用已注册的组合组件shape
        width: 200,
        height: 150,
        data: {
          width: 200,
          height: 150,
          isUserComponent: true,
          isJsonGroup: true,
          userComponentId: userComponent.key,
          userComponentName: userComponent.name,
          componentInfo: userComponent.componentInfo,
          renderType: 'json-group',
          // 标记需要在添加到画布后恢复内部结构
          needsInternalStructure: true
        }
      });
      
      dnd.value.start(groupNode, e);
    } else {
      // 创建普通用户组件（SVG类型）
      const componentNode = graph.value.createNode({
        shape: type,  // 直接使用组件ID作为shape（应该已经动态注册过）
        width: 120,
        height: 80,
        data: {
          width: 120,
          height: 80,
          isUserComponent: true,
          userComponentId: userComponent.key,
          userComponentName: userComponent.name,
          jsonData: userComponent.jsonData,
          svgData: userComponent.svgData
        }
      });
      
      dnd.value.start(componentNode, e);
    }
    return;
  }

  // 内置元件逻辑不变
  let nodeConfig: any = { shape: type };
  console.log('nodeConfig--------------',nodeConfig)
  if (type !== 'custom-port' && type !== 'custom-port-v2') {
    nodeConfig.ports = ports;
  }

  let node = graph.value.createNode(nodeConfig);
  dnd.value.start(node, e);
};

// 辅助函数：获取 cells 的整体包围盒
function getCellsBBox(cells: any[]) {
  const nodes = cells.filter(cell => cell.shape && cell.shape !== 'edge' && !cell.isEdge);
  if (nodes.length === 0) return { x: 0, y: 0, width: 0, height: 0 };
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
  nodes.forEach(node => {
    // 兼容不同的数据结构
    const x = node.position?.x ?? node.x ?? 0;
    const y = node.position?.y ?? node.y ?? 0;
    const width = node.size?.width ?? node.width ?? 0;
    const height = node.size?.height ?? node.height ?? 0;
    
    minX = Math.min(minX, x);
    minY = Math.min(minY, y);
    maxX = Math.max(maxX, x + width);
    maxY = Math.max(maxY, y + height);
  });
  return { x: minX, y: minY, width: maxX - minX, height: maxY - minY };
}
// 辅助函数：获取鼠标在画布上的坐标
function getMousePosition(e: MouseEvent, graph: any) {
  const { left, top } = graph.container.getBoundingClientRect();
  return {
    x: e.clientX - left + graph.scrollLeft,
    y: e.clientY - top + graph.scrollTop
  };
}
// 辅助函数：生成唯一 id
function generateId() {
  return 'user_node_' + Math.random().toString(36).substr(2, 9);
}

// 判断是否为电气元件
const electricalComponents = [
  'thyristor', 'diode', 'resistor', 'capacitor', 
  'inductor', 'transformer', 'switch', 'ground', 'voltage_source', 
  'current_source', 'motor', 'generator', 'fuse', 'relay'
];

const isElectricalComponent = (key: string) => {
  return electricalComponents.includes(key);
};

// 处理图片加载错误
const handleImageError = (e: any) => {
  console.warn('图片加载失败:', e.target.src);
  // 设置默认图片或隐藏图片
  e.target.style.display = 'none';
};

// 初始化所有用户组件的动态注册
const initUserComponents = () => {
  x6Store.userComponents.forEach((comp: UserComponent) => {
    try {
      if (comp.renderType === 'json' && comp.componentInfo) {
        // JSON组件注册
        registerDynamicJsonComponent(comp.id, comp.componentInfo, {
          width: 120,
          height: 80,
          name: comp.name
        });
        console.log(`注册JSON用户组件: ${comp.name}`);
      } else {
        // SVG组件注册（默认和兼容）
        registerDynamicSvg(comp.id, comp.svgData, {
          width: 120,
          height: 80,
          name: comp.name
        });
        console.log(`注册SVG用户组件: ${comp.name}`);
      }
    } catch (error) {
      console.warn(`注册用户组件 ${comp.name} 失败:`, error);
    }
  });
};

// 使用 watchEffect 自动追踪依赖并执行初始化
watchEffect(() => {
  if (graph.value) {
    initDnd();
  }
});

// 组件挂载时初始化用户组件注册
onMounted(() => {
  initUserComponents();
});
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;
  display: flex;
  background: transparent;
  .left {
    background: #1D2129;
    width: 60px;
    display: flex;
    flex-direction: column;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);

    p {
        cursor: pointer;
        margin: 0;
        width: 100%;
        height: 56px;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        opacity: 0.6;
        
        &:hover {
          opacity: 0.8;
          background: rgba(255, 255, 255, 0.04);
        }
        
        &::after {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 0;
          background: #4080FF;
          transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          border-radius: 0 2px 2px 0;
        }
        
        &:hover {
          background: #E8F3FF;
        }
        
        &.active {
          opacity: 1;
          background: rgba(255, 255, 255, 0.08);
          &::after {
            height: 16px;
          }
        }
        
        img {
          width: 24px;
          height: 24px;
          filter: brightness(0) invert(1);
        }
        
        &:hover img,
        &.active img {
          opacity: 1;
          transform: scale(1.05);
        }
    }
  }
  .right {
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow: hidden;
    background: #fff;

    :deep(.el-input) {
      width: 100%;
      height: 40px;
      border-radius: 4px;
      
      .el-input__wrapper {
        background: #fff;
        border: 1px solid #E5E6EB;
        box-shadow: none;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: #C9CDD4;
        }
        
        &.is-focus {
          border-color: #165DFF;
          box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.1);
        }
        
        input {
          color: #1D2129;
          &::placeholder {
            color: #86909C;
          }
        }
      }
    }
      .el-input__wrapper {
        box-shadow: none;
        background-color: transparent;
        border: 1px solid #3a4b62;
        input {
          border: none;
          color: #fff;
        }
      }
    }
    .supplies {
      flex: 1;
      overflow-y: auto;
      padding-right: 8px;
      
      &::-webkit-scrollbar {
        width: 4px;
      }
      
      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.02);
        border-radius: 2px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
        
        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }
      }
      
      .noEgImg {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        
        li {
          cursor: pointer;
          list-style: none;
          height: 90px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: #FFFFFF;
          border: 1px solid #E5E6EB;
          border-radius: 8px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          
          &:hover {
            background: #F7F8FA;
            border-color: #165DFF;
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(22, 93, 255, 0.1);
          }
          
          div {
            width: 40px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            
            img {
              width: 100%;
              height: 100%;
              opacity: 0.9;
              transition: all 0.3s ease;
            }

            .svg-icon {
              width: 100%;
              height: 100%;
              opacity: 0.9;
              transition: all 0.3s ease;
              display: flex;
              align-items: center;
              justify-content: center;
              overflow: hidden;
              position: relative;
              
              :deep(svg) {
                width: 36px;
                height: 36px;
                max-width: 36px;
                max-height: 36px;
              }
            }
          }
          
          span {
            margin-top: 8px;
            font-size: 13px;
            color: #1D2129;
            transition: all 0.3s ease;
          }
          
          &:hover {
            img {
              opacity: 1;
            }
            .svg-icon {
              opacity: 1;
            }
            span {
              color: #165DFF;
            }
          }
        }
      }
      
      .egImg {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        
        .eg-item {
          cursor: pointer;
          margin: 0;
          height: 90px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: #FFFFFF;
          border: 1px solid #E5E6EB;
          border-radius: 8px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          
          &:hover {
            background: #F7F8FA;
            border-color: #165DFF;
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(22, 93, 255, 0.1);
          }
          
          img {
            width: 40px;
            height: 40px;
            opacity: 0.9;
            transition: all 0.3s ease;
          }
          
          .svg-icon {
            width: 40px;
            height: 40px;
            opacity: 0.9;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
            
            :deep(svg) {
              width: 40px;
              height: 40px;
              max-width: 40px;
              max-height: 40px;
            }
          }
          
          span {
            margin-top: 8px;
            font-size: 13px;
            color: #1D2129;
            transition: all 0.3s ease;
          }
          
          &:hover {
            img {
              opacity: 1;
            }
            .svg-icon {
              opacity: 1;
            }
            span {
              color: #165DFF;
            }
          }
        }
      }
    }
  }

::-webkit-scrollbar {
  width: 1px;
  height: 1px;
}
::-webkit-scrollbar-thumb {
  background-color: #0c5fa6;
  border-radius: 10px;
}
</style>
