
// 导入新的电气元件SVG图标
import thyristor from "@/assets/images/thyristor.svg";
import diode from "@/assets/images/diode.svg";
import inductor from "@/assets/images/inductor.svg";
import capacitor from "@/assets/images/capacitor.svg";
import resistor from "@/assets/images/resistor.svg";
import transformer from "@/assets/images/transformer.svg";
import switchIcon from "@/assets/images/switch.svg";
import ground from "@/assets/images/ground.svg";
import voltageSource from "@/assets/images/voltage_source.svg";
import currentSource from "@/assets/images/current_source.svg";
import motor from "@/assets/images/motor.svg";
import generator from "@/assets/images/generator.svg";
import fuse from "@/assets/images/fuse.svg";
import { builtInNodeIcons, getIconDataUrl } from "./builtInNodeIcons";
import { builtInNodeList } from "../builtInNodes";


import {
  default as ybp1
} from "@/assets/images/ybpImg/ybp1.png";
// import pinIcon from "@/assets/images/pin.svg";

// 基础组件列表（静态）
const baseJcList = [
  // 电力电子器件
  {
    key: "thyristor",
    name: "晶闸管",
    img: thyristor,
  },
  {
    key: "diode",
    name: "二极管",
    img: diode,
  },
  // 基本电路元件
  {
    key: "resistor",
    name: "电阻",
    img: resistor,
  },
  {
    key: "capacitor",
    name: "电容",
    img: capacitor,
  },
  {
    key: "inductor",
    name: "电感",
    img: inductor,
  },
  {
    key: "transformer",
    name: "变压器",
    img: transformer,
  },
  // 电源和接地
  {
    key: "voltage_source",
    name: "电压源",
    img: voltageSource,
  },
  {
    key: "current_source",
    name: "电流源",
    img: currentSource,
  },
  {
    key: "ground",
    name: "接地",
    img: ground,
  },
  // 机电设备
  {
    key: "motor",
    name: "电机",
    img: motor,
  },
  {
    key: "generator",
    name: "发电机",
    img: generator,
  },
  // 保护和控制元件
  {
    key: "switch",
    name: "开关",
    img: switchIcon,
  },
  {
    key: "fuse",
    name: "熔断器",
    img: fuse,
  },
  // {
  //   key: "relay",
  //   name: "继电器",
  //   img: relay,
  // },
  
  // 自定义引脚
  {
    key: "custom-pin",
    name: "自定义引脚",
    img: getIconDataUrl(builtInNodeIcons['custom-pin'])
  },
];

// 动态组件列表（在运行时添加）
let dynamicComponents: any[] = [];

// 用于触发响应式更新的计数器
let updateCounter = 0;

// 添加动态组件的函数
export const addDynamicComponent = (componentInfo: any) => {
  const existingIndex = dynamicComponents.findIndex(comp => comp.key === componentInfo.id);
  
  const newComponent = {
    key: componentInfo.id,        // 使用唯一ID
    name: componentInfo.name,
    svgData: componentInfo.svgData,
    jsonData: componentInfo.jsonData, // 新增：JSON数据
    componentInfo: componentInfo.componentInfo, // 新增：组件信息
    renderType: componentInfo.renderType || 'svg', // 新增：渲染类型
    isDynamic: true
  };
  
  if (existingIndex !== -1) {
    // 如果已存在，则更新
    dynamicComponents[existingIndex] = newComponent;
  } else {
    // 如果不存在，则添加
    dynamicComponents.push(newComponent);
  }
  
  // 触发更新
  updateCounter++;
  
  console.log(`添加动态组件: ${componentInfo.name} (${componentInfo.id}), 渲染类型: ${newComponent.renderType}`);
};

// 移除动态组件的函数
export const removeDynamicComponent = (componentId: string) => {
  const index = dynamicComponents.findIndex(comp => comp.key === componentId);
  if (index !== -1) {
    dynamicComponents.splice(index, 1);
    console.log(`移除动态组件: ${componentId}`);
  }
};

// 获取完整列表的函数（用于实时更新）
const getFullJcList = () => {
  // 访问updateCounter确保响应式
  updateCounter;
  return [...baseJcList, ...dynamicComponents];
};

// 获取更新计数器（用于响应式监听）
export const getDynamicUpdateCounter = () => updateCounter;

const ybpList = [
  {
    key: "ybp1",
    name: "仪表盘1",
    img: ybp1,
  },
];

// 内置电力元件列表
const powerNodeList = builtInNodeList.map(node => ({
  key: node.key,
  name: node.name,
  img: getIconDataUrl(builtInNodeIcons[node.key as keyof typeof builtInNodeIcons] || builtInNodeIcons['power-rect']),
}));

// 合并基础组件和电力元件（初始静态列表）
const jcListWithPower = [...baseJcList, ...powerNodeList];

// 导出初始列表（保持兼容性）
export { jcListWithPower as jcList, ybpList };

// 导出动态获取列表的函数
export { getFullJcList };
