import { Graph, Node } from '@antv/x6';

// 点到线段的距离计算
interface Point {
  x: number;
  y: number;
}

interface LineSegment {
  start: Point;
  end: Point;
}

interface PolygonEdge extends LineSegment {
  type: 'horizontal' | 'vertical' | 'diagonal';
  length: number;
}

export class AdvancedDragPortManager {
  private graph: Graph;
  private readonly SNAP_DISTANCE = 50; // 吸附距离阈值
  // private readonly EDGE_TOLERANCE = 15; // 边缘容差
  private selectedPin: Node | null = null;

  constructor(graph: Graph) {
    this.graph = graph;
    this.setupEventListeners();
  }

  private setupEventListeners() {
    // 监听节点添加事件
    this.graph.on('node:added', ({ node }) => {
      if (node.shape === 'custom-pin') {
        setTimeout(() => {
          this.handlePinDropped(node);
        }, 100);
      }
    });

    // 添加实时预览功能
    this.graph.on('node:moving', ({ node }) => {
      if (node.shape === 'custom-pin') {
       // this.showSnapPreview(node);
      }
    });

    // 监听节点选择
    this.graph.on('node:selected', ({ node }: { node: any }) => {
      if (node.shape === 'custom-pin') {
        // 如果已有选中的引脚，先取消选择
        if (this.selectedPin && this.selectedPin !== node) {
          (this.graph as any).cleanSelection([node]);
        }
        this.selectedPin = node;
      } else {
        // 选择其他节点时，取消引脚选择
        if (this.selectedPin) {
          (this.graph as any).cleanSelection();
          this.selectedPin = null;
        }
      }
    });

    this.graph.on('node:unselected', ({ node }: { node: any }) => {
      if (node.shape === 'custom-pin' && this.selectedPin === node) {
        this.selectedPin = null;
      }
    });

    // 点击空白区域取消选择
    this.graph.on('blank:click', () => {
      if (this.selectedPin) {
        (this.graph as any).cleanSelection();
        this.selectedPin = null;
      }
    });

    // 使用 DOM 事件监听键盘输入
     document.addEventListener('keydown', (e: KeyboardEvent) => {
       // Escape键取消选择
       if (e.key === 'Escape' && this.selectedPin) {
         e.preventDefault();
         (this.graph as any).cleanSelection();
         this.selectedPin = null;
         return;
       }

       if (!this.selectedPin) return;

       const moveStep = 1; // 每次移动1px
       const currentPos = this.selectedPin.getPosition();

       switch (e.key) {
         case 'ArrowUp':
           e.preventDefault();
           this.selectedPin.position(currentPos.x, currentPos.y - moveStep);
           break;
         case 'ArrowDown':
           e.preventDefault();
           this.selectedPin.position(currentPos.x, currentPos.y + moveStep);
           break;
         case 'ArrowLeft':
           e.preventDefault();
           this.selectedPin.position(currentPos.x - moveStep, currentPos.y);
           break;
         case 'ArrowRight':
           e.preventDefault();
           this.selectedPin.position(currentPos.x + moveStep, currentPos.y);
           break;
       }
     });
  }



  private handlePinDropped(pinNode: Node) {
    const pinPos = pinNode.getPosition();
    console.log('🔧 处理引脚放置，位置:', pinPos);
    
    const nodes = this.graph.getNodes().filter(n => 
      n.id !== pinNode.id && n.shape !== 'custom-pin'
    );
    
    let bestAttachment: {
      node: Node;
      snapPoint: Point;
      distance: number;
      edgeInfo: any;
    } | null = null;
    
    let minDistance = Infinity;
    
    for (const node of nodes) {
      const attachment = this.findBestSnapPoint(pinPos, node);
      if (attachment && attachment.distance < minDistance && attachment.distance < this.SNAP_DISTANCE) {
        minDistance = attachment.distance;
        bestAttachment = {
          node,
          snapPoint: attachment.snapPoint,
          distance: attachment.distance,
          edgeInfo: attachment.edgeInfo
        };
      }
    }
    
    if (bestAttachment) {
      console.log('✅ 找到最佳附着点:', bestAttachment);
      this.attachPinToNode(pinNode, bestAttachment);
    } else {
      console.log('❌ 未找到合适的附着点，删除引脚');
      pinNode.remove();
    }
  }

  private findBestSnapPoint(pinPos: Point, node: Node): {
    snapPoint: Point;
    distance: number;
    edgeInfo: any;
  } | null {
    const bbox = node.getBBox();
    const shape = node.shape;
    
    console.log(`🔍 分析节点 ${shape}:`, bbox);
    
    switch (shape) {
      case 'power-triangle':
        return this.findTriangleSnapPoint(pinPos, bbox);
      case 'power-diamond':
        return this.findDiamondSnapPoint(pinPos, bbox);
      case 'power-circle':
        return this.findCircleSnapPoint(pinPos, bbox);
      case 'power-ellipse':
        return this.findEllipseSnapPoint(pinPos, bbox);
      default:
        return this.findRectSnapPoint(pinPos, bbox);
    }
  }

  // 三角形精确斜边计算
  private findTriangleSnapPoint(pinPos: Point, bbox: any): {
    snapPoint: Point;
    distance: number;
    edgeInfo: any;
  } | null {
    const { x, y, width, height } = bbox;
    
    // 标准三角形顶点：refPoints='50,0 100,100 0,100'
    // 转换为实际坐标
    const vertices: Point[] = [
      { x: x + width * 0.5, y: y },           // 顶点 (50,0)
      { x: x + width, y: y + height },        // 右下 (100,100)  
      { x: x, y: y + height }                 // 左下 (0,100)
    ];
    
    console.log('🔺 三角形顶点:', vertices);
    
    // 三条边
    const edges: PolygonEdge[] = [
      {
        start: vertices[0], end: vertices[1],
        type: 'diagonal', 
        length: this.getDistance(vertices[0], vertices[1])
      }, // 左斜边
      {
        start: vertices[1], end: vertices[2],
        type: 'horizontal',
        length: this.getDistance(vertices[1], vertices[2])
      }, // 底边
      {
        start: vertices[2], end: vertices[0],
        type: 'diagonal',
        length: this.getDistance(vertices[2], vertices[0])
      }  // 右斜边
    ];
    
    let bestSnap: { snapPoint: Point; distance: number; edgeInfo: any } | null = null;
    let minDistance = Infinity;
    
    edges.forEach((edge, index) => {
      const snapResult = this.getClosestPointOnLineSegment(pinPos, edge);
      console.log(`边 ${index} (${edge.type}) 距离:`, snapResult.distance);
      
      if (snapResult.distance < minDistance) {
        minDistance = snapResult.distance;
        bestSnap = {
          snapPoint: snapResult.point,
          distance: snapResult.distance,
          edgeInfo: {
            edgeIndex: index,
            edgeType: edge.type,
            ratio: snapResult.ratio
          }
        };
      }
    });
    
    return bestSnap;
  }

  // 菱形精确斜边计算
  private findDiamondSnapPoint(pinPos: Point, bbox: any): {
    snapPoint: Point;
    distance: number;
    edgeInfo: any;
  } | null {
    const { x, y, width, height } = bbox;
    
    // 菱形顶点：refPoints='50,0 100,50 50,100 0,50'
    const vertices: Point[] = [
      { x: x + width * 0.5, y: y },           // 上 (50,0)
      { x: x + width, y: y + height * 0.5 },  // 右 (100,50)
      { x: x + width * 0.5, y: y + height },  // 下 (50,100)
      { x: x, y: y + height * 0.5 }           // 左 (0,50)
    ];
    
    const edges: PolygonEdge[] = [
      { start: vertices[0], end: vertices[1], type: 'diagonal', length: 0 },
      { start: vertices[1], end: vertices[2], type: 'diagonal', length: 0 },
      { start: vertices[2], end: vertices[3], type: 'diagonal', length: 0 },
      { start: vertices[3], end: vertices[0], type: 'diagonal', length: 0 }
    ];
    
    let bestSnap = null;
    let minDistance = Infinity;
    
    edges.forEach((edge, index) => {
      const snapResult = this.getClosestPointOnLineSegment(pinPos, edge);
      if (snapResult.distance < minDistance) {
        minDistance = snapResult.distance;
        bestSnap = {
          snapPoint: snapResult.point,
          distance: snapResult.distance,
          edgeInfo: { edgeIndex: index, edgeType: edge.type }
        };
      }
    });
    
    return bestSnap;
  }

  // 通用多边形处理 - 改进版本
  // private findPolygonSnapPoint(pinPos: Point, bbox: any, node: Node): {
  //   snapPoint: Point;
  //   distance: number;
  //   edgeInfo: any;
  // } | null {
  //   console.log('🔍 分析多边形节点:', node);
    
  //   // 尝试多种方法获取refPoints
  //   let refPoints = '';
    
  //   // 方法1: 从body/refPoints获取
  //   const bodyRefPoints = node.attr('body/refPoints');
  //   if (bodyRefPoints && typeof bodyRefPoints === 'string') {
  //     refPoints = bodyRefPoints;
  //     console.log('📍 从body/refPoints获取:', refPoints);
  //   }
    
  //   // 方法2: 从attrs.body.refPoints获取
  //   if (!refPoints) {
  //     const attrsRefPoints = node.getAttrs()?.body?.refPoints;
  //     if (attrsRefPoints && typeof attrsRefPoints === 'string') {
  //       refPoints = attrsRefPoints;
  //       console.log('📍 从attrs.body.refPoints获取:', refPoints);
  //     }
  //   }
    
  //   // 方法3: 从节点数据中获取
  //   if (!refPoints) {
  //     const nodeData = node.getData();
  //     if (nodeData && nodeData.refPoints) {
  //       refPoints = nodeData.refPoints;
  //       console.log('📍 从节点数据获取:', refPoints);
  //     }
  //   }
    
  //   // 方法4: 预定义的多边形形状
  //   if (!refPoints) {
  //     const shapeDefaults: { [key: string]: string } = {
  //       'power-hexagon': '50,0 100,25 100,75 50,100 0,75 0,25',
  //       'power-octagon': '30,0 70,0 100,30 100,70 70,100 30,100 0,70 0,30',
  //       'power-pentagon': '50,0 95,35 80,90 20,90 5,35',
  //       'power-star': '50,0 61,35 98,35 68,57 79,91 50,70 21,91 32,57 2,35 39,35'
  //     };
      
  //     const nodeShape = node.shape || '';
  //     if (shapeDefaults[nodeShape]) {
  //       refPoints = shapeDefaults[nodeShape];
  //       console.log('📍 使用预定义形状:', nodeShape, refPoints);
  //     }
  //   }
    
  //   // 方法5: 默认八边形
  //   if (!refPoints) {
  //     refPoints = '30,0 70,0 100,30 100,70 70,100 30,100 0,70 0,30';
  //     console.log('📍 使用默认八边形:', refPoints);
  //   }
    
  //   console.log('🎯 最终使用的refPoints:', refPoints);
    
  //   try {
  //     const vertices = this.parseRefPoints(refPoints, bbox);
  //     console.log('📐 解析的顶点:', vertices);
      
  //     if (vertices.length < 3) {
  //       console.warn('⚠️ 顶点数量不足，无法构成多边形');
  //       return null;
  //     }
      
  //     const edges: PolygonEdge[] = [];
  //     for (let i = 0; i < vertices.length; i++) {
  //       const start = vertices[i];
  //       const end = vertices[(i + 1) % vertices.length];
  //       edges.push({
  //         start, end,
  //         type: this.getEdgeType(start, end),
  //         length: this.getDistance(start, end)
  //       });
  //     }
      
  //     console.log('📏 多边形边:', edges.map((e, i) => `边${i}: ${e.type}, 长度${e.length.toFixed(1)}`));
      
  //     let bestSnap = null;
  //     let minDistance = Infinity;
      
  //     edges.forEach((edge, index) => {
  //       const snapResult = this.getClosestPointOnLineSegment(pinPos, edge);
  //       console.log(`边 ${index} (${edge.type}) 距离:`, snapResult.distance.toFixed(2));
        
  //       if (snapResult.distance < minDistance) {
  //         minDistance = snapResult.distance;
  //         bestSnap = {
  //           snapPoint: snapResult.point,
  //           distance: snapResult.distance,
  //           edgeInfo: { edgeIndex: index, edgeType: edge.type, ratio: snapResult.ratio }
  //         };
  //       }
  //     });
      
  //     return bestSnap;
      
  //   } catch (error) {
  //     console.error('❌ 多边形处理错误:', error);
  //     return null;
  //   }
  // }

  // 圆形附着点计算
  private findCircleSnapPoint(pinPos: Point, bbox: any): {
    snapPoint: Point;
    distance: number;
    edgeInfo: any;
  } | null {
    const centerX = bbox.x + bbox.width / 2;
    const centerY = bbox.y + bbox.height / 2;
    const radius = bbox.width / 2;
    
    const angle = Math.atan2(pinPos.y - centerY, pinPos.x - centerX);
    const snapPoint = {
      x: centerX + radius * Math.cos(angle),
      y: centerY + radius * Math.sin(angle)
    };
    
    const distance = this.getDistance(pinPos, snapPoint);
    
    return {
      snapPoint,
      distance,
      edgeInfo: { type: 'circle', angle }
    };
  }

  // 椭圆附着点计算
  private findEllipseSnapPoint(pinPos: Point, bbox: any): {
    snapPoint: Point;
    distance: number;
    edgeInfo: any;
  } | null {
    const centerX = bbox.x + bbox.width / 2;
    const centerY = bbox.y + bbox.height / 2;
    const a = bbox.width / 2;
    const b = bbox.height / 2;
    
    const angle = Math.atan2(pinPos.y - centerY, pinPos.x - centerX);
    const snapPoint = {
      x: centerX + a * Math.cos(angle),
      y: centerY + b * Math.sin(angle)
    };
    
    const distance = this.getDistance(pinPos, snapPoint);
    
    return {
      snapPoint,
      distance,
      edgeInfo: { type: 'ellipse', angle }
    };
  }

  // 矩形附着点计算
  private findRectSnapPoint(pinPos: Point, bbox: any): {
    snapPoint: Point;
    distance: number;
    edgeInfo: any;
  } | null {
    const { x, y, width, height } = bbox;
    
    const edges: PolygonEdge[] = [
      { start: { x, y }, end: { x: x + width, y }, type: 'horizontal', length: width },
      { start: { x: x + width, y }, end: { x: x + width, y: y + height }, type: 'vertical', length: height },
      { start: { x: x + width, y: y + height }, end: { x, y: y + height }, type: 'horizontal', length: width },
      { start: { x, y: y + height }, end: { x, y }, type: 'vertical', length: height }
    ];
    
    let bestSnap = null;
    let minDistance = Infinity;
    
    edges.forEach((edge, index) => {
      const snapResult = this.getClosestPointOnLineSegment(pinPos, edge);
      if (snapResult.distance < minDistance) {
        minDistance = snapResult.distance;
        bestSnap = {
          snapPoint: snapResult.point,
          distance: snapResult.distance,
          edgeInfo: { edgeIndex: index, edgeType: edge.type }
        };
      }
    });
    
    return bestSnap;
  }

  // 核心算法：计算点到线段的最短距离和最近点
  private getClosestPointOnLineSegment(point: Point, segment: LineSegment): {
    point: Point;
    distance: number;
    ratio: number;
  } {
    const { start, end } = segment;
    
    // 线段向量
    const segmentLength = this.getDistance(start, end);
    if (segmentLength === 0) {
      return {
        point: start,
        distance: this.getDistance(point, start),
        ratio: 0
      };
    }
    
    // 计算投影比例 t
    const dx = end.x - start.x;
    const dy = end.y - start.y;
    const t = Math.max(0, Math.min(1, 
      ((point.x - start.x) * dx + (point.y - start.y) * dy) / (dx * dx + dy * dy)
    ));
    
    // 计算最近点
    const closestPoint = {
      x: start.x + t * dx,
      y: start.y + t * dy
    };
    
    const distance = this.getDistance(point, closestPoint);
    
    return {
      point: closestPoint,
      distance,
      ratio: t
    };
  }

  // 工具函数：计算两点距离
  private getDistance(p1: Point, p2: Point): number {
    return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
  }

  // 工具函数：判断边的类型
  // private getEdgeType(start: Point, end: Point): 'horizontal' | 'vertical' | 'diagonal' {
  //   const dx = Math.abs(end.x - start.x);
  //   const dy = Math.abs(end.y - start.y);
    
  //   if (dy < 1) return 'horizontal';
  //   if (dx < 1) return 'vertical';
  //   return 'diagonal';
  // }

  // 工具函数：解析 refPoints
  // private parseRefPoints(refPoints: string, bbox: any): Point[] {
  //   const points = refPoints.split(' ');
  //   return points.map(point => {
  //     const [x, y] = point.split(',').map(Number);
  //     return {
  //       x: bbox.x + (x / 100) * bbox.width,
  //       y: bbox.y + (y / 100) * bbox.height
  //     };
  //   });
  // }

  // 实时预览功能
  // private showSnapPreview(pinNode: Node) {
  //   const pinPos = pinNode.getPosition();
  //   const nodes = this.graph.getNodes().filter(n => 
  //     n.id !== pinNode.id && n.shape !== 'custom-pin'
  //   );
    
  //   // 移除之前的预览
  //   this.removeSnapPreview();
    
  //   for (const node of nodes) {
  //     const attachment = this.findBestSnapPoint(pinPos, node);
  //     if (attachment && attachment.distance < this.SNAP_DISTANCE) {
  //       this.showPreviewMarker(attachment.snapPoint);
  //       break;
  //     }
  //   }
  // }

  // 显示预览标记
  // private showPreviewMarker(point: Point) {
  //   this.graph.addNode({
  //     id: 'snap-preview-marker',
  //     shape: 'circle',
  //     x: point.x - 3,
  //     y: point.y - 3,
  //     width: 6,
  //     height: 6,
  //     zIndex: 1000,
  //     attrs: {
  //       body: {
  //         fill: '#52C41A',
  //         stroke: '#389E0D',
  //         strokeWidth: 1
  //       }
  //     }
  //   });
  // }

  // 移除预览标记
  private removeSnapPreview() {
    const previewMarker = this.graph.getCellById('snap-preview-marker');
    if (previewMarker) {
      previewMarker.remove();
    }
  }

  // 附着引脚到节点
  private attachPinToNode(pinNode: Node, attachment: any) {
    const { node: targetNode, snapPoint, edgeInfo } = attachment;
    const bbox = targetNode.getBBox();
    
    // 计算相对位置
    const relativeX = snapPoint.x - bbox.x;
    const relativeY = snapPoint.y - bbox.y;
    
    console.log('📌 附着引脚到相对位置:', { x: relativeX, y: relativeY });
    
    // 将引脚节点移动到吸附点，使其中心对齐
    const pinSize = pinNode.getSize();
    pinNode.position(snapPoint.x - pinSize.width / 2, snapPoint.y - pinSize.height / 2);

    // 在这里，您可以选择将pinNode与targetNode关联起来，以便后续操作
    // 例如，可以将其存储在targetNode的data中
    const attachedPins = targetNode.getData()?.attachedPins || [];
    attachedPins.push(pinNode.id);
    targetNode.setData({ ...targetNode.getData(), attachedPins });

    // 暂时不删除pinNode，因为它现在代表一个可编辑的引脚
    // pinNode.remove();
    
    // 移除预览
    this.removeSnapPreview();
    
    console.log('✅ 引脚成功附着到', targetNode.shape, '边缘类型:', edgeInfo.edgeType);
  }
}