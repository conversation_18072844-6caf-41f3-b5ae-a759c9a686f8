import { register } from "@antv/x6-vue-shape";
import { Graph } from "@antv/x6";
import { jcCustom } from "./custom";
import { ports } from "./config";
import { registerAllBuiltInNodes } from "./builtInNodes";





export function jcRegister() {
  // 注册基础组件

  register({
    shape: "line",
    width: 80,
    height: 60,
    data: {
      width: 80,
      height: 60,
    },
    component: jcCustom.jcLine,
  });

  register({
    shape: "path",
    width: 80,
    height: 60,
    data: {
      width: 80,
      height: 60,
    },
    component: jcCustom.jcPath,
  });

  register({
    shape: "text",
    width: 80,
    height: 60,
    data: {
      width: 80,
      height: 60,
    },
    component: jcCustom.jcText,
  });



  // 注册电气元件
  register({
    shape: "thyristor",
    width: 40,
    height: 60,
    data: { width: 40, height: 60 },
    component: jcCustom.jcThyristor,
    ports: {
      groups: {
        absolute: {
          position: 'absolute',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: {
                visibility: 'visible',
                cursor: 'crosshair',
              },
            },
          },
        },

      },
      items: [
        { id: 'top', group: 'absolute', args: { x: 20, y: 4 } },
        { id: 'bottom', group: 'absolute', args: { x: 20, y: 56 } },
        { id: 'left', group: 'absolute', args: { x: 4, y: 25 } },
      ],
    },
  });

  register({
    shape: "diode",
    width: 40,
    height: 60,
    data: { width: 40, height: 60 },
    component: jcCustom.jcDiode,
    ports: {
      groups: {
        absolute: {
          position: 'absolute',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: { visibility: 'visible', cursor: 'crosshair' },
            },
          },
        },
      },
      items: [
        { id: 'top', group: 'absolute', args: { x: 20, y: 5 } },
        { id: 'bottom', group: 'absolute', args: { x: 20, y: 55 } },
      ],
    },
  });

  register({
    shape: "transistor",
    width: 40,
    height: 60,
    data: { width: 40, height: 60 },
    component: jcCustom.jcTransistor,
    ports: {
      groups: {
        absolute: {
          position: 'absolute',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: { visibility: 'visible', cursor: 'crosshair' },
            },
          },
        },
      },
      items: [
        { id: 'top', group: 'absolute', args: { x: 15, y: 5 } },
        { id: 'bottom', group: 'absolute', args: { x: 15, y: 55 } },
        { id: 'rightTop', group: 'absolute', args: { x: 30, y: 5 } },
        { id: 'rightBottom', group: 'absolute', args: { x: 30, y: 55 } },
      ],
    },
  });

  register({
    shape: "resistor",
    width: 60,
    height: 40,
    data: { width: 60, height: 40 },
    component: jcCustom.jcResistor,
    ports: {
      groups: {
        absolute: {
          position: 'absolute',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: { visibility: 'visible', cursor: 'crosshair' },
            },
          },
        },
      },
      items: [
        { id: 'left', group: 'absolute', args: { x: 5, y: 20 } },
        { id: 'right', group: 'absolute', args: { x: 55, y: 20 } },
      ],
    },
  });

  register({
    shape: "capacitor",
    width: 40,
    height: 60,
    data: { width: 40, height: 60 },
    component: jcCustom.jcCapacitor,
    ports: {
      groups: {
        absolute: {
          position: 'absolute',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: { visibility: 'visible', cursor: 'crosshair' },
            },
          },
        },
      },
      items: [
        { id: 'top', group: 'absolute', args: { x: 20, y: 5 } },
        { id: 'bottom', group: 'absolute', args: { x: 20, y: 55 } },
      ],
    },
  });

  register({
    shape: "inductor",
    width: 60,
    height: 40,
    data: { width: 60, height: 40 },
    component: jcCustom.jcInductor,
    ports: {
      groups: {
        absolute: {
          position: 'absolute',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: { visibility: 'visible', cursor: 'crosshair' },
            },
          },
        },
      },
      items: [
        { id: 'left', group: 'absolute', args: { x: 5, y: 20 } },
        { id: 'right', group: 'absolute', args: { x: 55, y: 20 } },
      ],
    },
  });

  register({
    shape: "transformer",
    width: 60,
    height: 60,
    data: { width: 60, height: 60 },
    component: jcCustom.jcTransformer,
    ports: {
      groups: {
        absolute: {
          position: 'absolute',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: { visibility: 'visible', cursor: 'crosshair' },
            },
          },
        },
      },
      items: [
        { id: 'leftTop', group: 'absolute', args: { x: 20, y: 5 } },
        { id: 'leftBottom', group: 'absolute', args: { x: 20, y: 55 } },
        { id: 'rightTop', group: 'absolute', args: { x: 40, y: 5 } },
        { id: 'rightBottom', group: 'absolute', args: { x: 40, y: 55 } },
      ],
    },
  });

  register({
    shape: "switch",
    width: 60,
    height: 40,
    data: { width: 60, height: 40 },
    component: jcCustom.jcSwitch,
    ports: {
      groups: {
        absolute: {
          position: 'absolute',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: { visibility: 'visible', cursor: 'crosshair' },
            },
          },
        },
      },
      items: [
        { id: 'left', group: 'absolute', args: { x: 5, y: 20 } },
        { id: 'right', group: 'absolute', args: { x: 55, y: 20 } },
      ],
    },
  });

  register({
    shape: "ground",
    width: 40,
    height: 40,
    data: { width: 40, height: 40 },
    component: jcCustom.jcGround,
    ports: {
      groups: {
        absolute: {
          position: 'absolute',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: { visibility: 'visible', cursor: 'crosshair' },
            },
          },
        },
      },
      items: [
        { id: 'top', group: 'absolute', args: { x: 20, y: 5 } },
      ],
    },
  });

  register({
    shape: "voltage_source",
    width: 40,
    height: 40,
    data: { width: 40, height: 40 },
    component: jcCustom.jcVoltageSource,
    ports: {
      groups: {
        absolute: {
          position: 'absolute',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: { visibility: 'visible', cursor: 'crosshair' },
            },
          },
        },
      },
      items: [
        { id: 'top', group: 'absolute', args: { x: 20, y: 5 } },
        { id: 'bottom', group: 'absolute', args: { x: 20, y: 35 } },
      ],
    },
  });

  register({
    shape: "current_source",
    width: 40,
    height: 40,
    data: { width: 40, height: 40 },
    component: jcCustom.jcCurrentSource,
    ports: {
      groups: {
        absolute: {
          position: 'absolute',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: { visibility: 'visible', cursor: 'crosshair' },
            },
          },
        },
      },
      items: [
        { id: 'top', group: 'absolute', args: { x: 20, y: 5 } },
        { id: 'bottom', group: 'absolute', args: { x: 20, y: 35 } },
      ],
    },
  });

  register({
    shape: "motor",
    width: 50,
    height: 50,
    data: { width: 50, height: 50 },
    component: jcCustom.jcMotor,
    ports: {
      groups: {
        absolute: {
          position: 'absolute',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: { visibility: 'visible', cursor: 'crosshair' },
            },
          },
        },
      },
      items: [
        { id: 'top', group: 'absolute', args: { x: 25, y: 5 } },
        { id: 'bottom', group: 'absolute', args: { x: 25, y: 45 } },
        { id: 'left', group: 'absolute', args: { x: 5, y: 25 } },
        { id: 'right', group: 'absolute', args: { x: 45, y: 25 } },
      ],
    },
  });

  register({
    shape: "generator",
    width: 50,
    height: 50,
    data: { width: 50, height: 50 },
    component: jcCustom.jcGenerator,
    ports: {
      groups: {
        absolute: {
          position: 'absolute',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: { visibility: 'visible', cursor: 'crosshair' },
            },
          },
        },
      },
      items: [
        { id: 'top', group: 'absolute', args: { x: 25, y: 5 } },
        { id: 'left', group: 'absolute', args: { x: 5, y: 25 } },
      ],
    },
  });

  register({
    shape: "fuse",
    width: 60,
    height: 40,
    data: { width: 60, height: 40 },
    component: jcCustom.jcFuse,
    ports: {
      groups: {
        absolute: {
          position: 'absolute',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: { visibility: 'visible', cursor: 'crosshair' },
            },
          },
        },
      },
      items: [
        { id: 'left', group: 'absolute', args: { x: 5, y: 20 } },
        { id: 'right', group: 'absolute', args: { x: 55, y: 20 } },
      ],
    },
  });

  register({
    shape: "relay",
    width: 60,
    height: 60,
    data: { width: 60, height: 60 },
    component: jcCustom.jcRelay,
    ports: {
      groups: {
        absolute: {
          position: 'absolute',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff',
              style: { visibility: 'visible', cursor: 'crosshair' },
            },
          },
        },
      },
      items: [
        { id: 'left', group: 'absolute', args: { x: 5, y: 30 } },
        { id: 'right', group: 'absolute', args: { x: 55, y: 30 } },
      ],
    },
  });

  // 自定义引脚 - 简化设计，24x24尺寸
  register({
    shape: "custom-pin",
    width: 24,
    height: 24,
    data: { width: 24, height: 24 },
    component: jcCustom.jcCustomPin,
    zIndex: 100, // 设置引脚的默认层级为100，确保在其他元件之上
    // 引脚本身不需要ports，它就是一个引脚
  });
}

// 注册仪表盘组件


// 注册内置矩形节点（带连接桩）
const registerBuiltInRect = () => {
  Graph.registerNode(
    'custom-rect',
    {
      inherit: 'rect', // 继承内置的rect节点
      width: 100,
      height: 60,
      attrs: {
        body: {
          strokeWidth: 2,
          stroke: '#5F95FF',
          fill: '#EFF4FF',
          rx: 4,
          ry: 4,
        },
        text: {
          fontSize: 12,
          fill: '#262626',
        },
      },
      ports: {
        ...ports,
      },
    },
    true // 覆盖同名节点
  );
};

// 注册 group 节点
const registerGroupNode = () => {
  Graph.registerNode(
    'group',
    {
      inherit: 'rect',
      width: 200,
      height: 120,
      attrs: {
        body: {
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: '#EFF4FF',
          rx: 8,
          ry: 8,
        },
        label: {
          fontSize: 14,
          fill: '#333',
        },
      },
      zIndex: 1,
    },
    true
  );
};

const registerInit = () => {
  jcRegister();
  registerBuiltInRect(); // 注册内置矩形（保留兼容）
  registerAllBuiltInNodes(); // 注册所有内置节点
  registerGroupNode(); // 注册 group 节点
};

// 安全的SVG预处理函数
const preprocessSvg = (svgData: string) => {
  try {
    console.log('开始预处理SVG数据');
    let processedSvg = svgData;

    // 1. 处理viewBox中的百分比值
    processedSvg = processedSvg.replace(/viewBox="([^"]*%[^"]*)"/g, '');
    console.log('已移除包含百分比的viewBox');

    // 2. 处理可能的XML命名空间问题
    processedSvg = processedSvg.replace(/xmlns:xlink="[^"]*"/g, '');
    processedSvg = processedSvg.replace(/xlink:href/g, 'href');
    console.log('已清理XML命名空间');

    // 3. 处理可能的样式问题
    processedSvg = processedSvg.replace(/style="[^"]*border:[^"]*"/g, '');
    processedSvg = processedSvg.replace(/style="[^"]*cursor:[^"]*"/g, '');
    processedSvg = processedSvg.replace(/style="[^"]*font:[^"]*"/g, '');
    console.log('已清理样式属性');

    // 4. 确保SVG有正确的根元素
    if (!processedSvg.includes('<svg')) {
      console.warn('SVG数据不包含svg根元素，添加默认svg包装');
      processedSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100">${processedSvg}</svg>`;
    }

    console.log('SVG预处理完成，长度变化:', svgData.length - processedSvg.length);
    return processedSvg;

  } catch (error) {
    console.error('SVG预处理失败:', error);
    return svgData; // 如果预处理失败，返回原始数据
  }
};

// 解析SVG数据中的引脚信息
const parsePinsFromSvg = (svgData: string) => {
  const pins: any[] = [];

  console.log('开始解析SVG引脚信息，SVG长度:', svgData.length);

  // 先标准化ViewBox，确保坐标系正确
  const normalizedSvg = normalizeViewBox(svgData);

  // 获取标准化后的viewBox
  const viewBoxMatch = normalizedSvg.match(/viewBox="([^"]+)"/);
  let viewBox = { x: 0, y: 0, width: 100, height: 100 };

  if (viewBoxMatch) {
    const [x, y, width, height] = viewBoxMatch[1].split(' ').map(Number);
    viewBox = { x, y, width, height };
  }

  console.log('标准化后的ViewBox:', viewBox);

  // 解析引脚信息 - 支持多种引脚格式
  let match;
  let index = 0;

  // 方法1: 查找包含data-shape="custom-pin"的g元素
  const pinRegex1 = /<g[^>]*data-shape="custom-pin"[^>]*transform="translate\(([^,]+),([^)]+)\)"[^>]*>/g;
  while ((match = pinRegex1.exec(svgData)) !== null) {
    try {
      const pinX = parseFloat(match[1]);
      const pinY = parseFloat(match[2]);

      // 引脚中心点 - 新的引脚是24x24，中心在(12,12)
      const pinCenterX = pinX + 12;
      const pinCenterY = pinY + 12;

      // 计算引脚中心相对于标准化viewBox的相对位置（0-1）
      const relativeX = (pinCenterX - viewBox.x) / viewBox.width;
      const relativeY = (pinCenterY - viewBox.y) / viewBox.height;

      // 简化的边缘判断 - 直接使用相对位置
      let side = 'center';

      // 判断最接近哪个边
      const distanceToLeft = relativeX;
      const distanceToRight = 1 - relativeX;
      const distanceToTop = relativeY;
      const distanceToBottom = 1 - relativeY;

      const minDistance = Math.min(distanceToLeft, distanceToRight, distanceToTop, distanceToBottom);

      if (minDistance === distanceToLeft) {
        side = 'left';
      } else if (minDistance === distanceToRight) {
        side = 'right';
      } else if (minDistance === distanceToTop) {
        side = 'top';
      } else {
        side = 'bottom';
      }

      pins.push({
        id: `pin-${index}`,
        side: side,
        relativeX: relativeX,
        relativeY: relativeY,
        centerX: pinCenterX,
        centerY: pinCenterY
      });

      console.log(`引脚 ${index}: 原始位置(${pinCenterX}, ${pinCenterY}), 相对位置(${relativeX.toFixed(2)}, ${relativeY.toFixed(2)}), 边: ${side}`);

      index++;
    } catch (error) {
      console.warn('解析引脚位置失败:', error, '匹配结果:', match);
    }
  }

  // 方法2: 如果方法1没有找到，尝试查找其他格式的引脚
  if (pins.length === 0) {
    console.log('方法1未找到引脚，尝试方法2');
    const pinRegex2 = /<g[^>]*custom-pin[^>]*transform="translate\(([^,]+),([^)]+)\)"[^>]*>/gi;
    while ((match = pinRegex2.exec(svgData)) !== null) {
      try {
        const pinX = parseFloat(match[1]);
        const pinY = parseFloat(match[2]);

        const pinCenterX = pinX + 12;
        const pinCenterY = pinY + 12;

        const relativeX = (pinCenterX - viewBox.x) / viewBox.width;
        const relativeY = (pinCenterY - viewBox.y) / viewBox.height;

        let side = 'center';
        const distanceToLeft = relativeX;
        const distanceToRight = 1 - relativeX;
        const distanceToTop = relativeY;
        const distanceToBottom = 1 - relativeY;

        const minDistance = Math.min(distanceToLeft, distanceToRight, distanceToTop, distanceToBottom);

        if (minDistance === distanceToLeft) {
          side = 'left';
        } else if (minDistance === distanceToRight) {
          side = 'right';
        } else if (minDistance === distanceToTop) {
          side = 'top';
        } else {
          side = 'bottom';
        }

        pins.push({
          id: `pin-${index}`,
          side: side,
          relativeX: relativeX,
          relativeY: relativeY,
          centerX: pinCenterX,
          centerY: pinCenterY
        });

        console.log(`引脚 ${index} (方法2): 原始位置(${pinCenterX}, ${pinCenterY}), 相对位置(${relativeX.toFixed(2)}, ${relativeY.toFixed(2)}), 边: ${side}`);

        index++;
      } catch (error) {
        console.warn('方法2解析引脚位置失败:', error, '匹配结果:', match);
      }
    }
  }

  console.log(`总共解析到 ${pins.length} 个引脚`);

  return { pins, viewBox };
};

// 简化的引脚移除 - 只需要移除引脚图形，没有文字需要处理
const removePinsFromSvg = (svgData: string) => {
  let processedSvg = svgData;

  console.log('开始移除引脚，原始SVG长度:', processedSvg.length);
  console.log('移除前是否包含custom-pin:', processedSvg.includes('custom-pin'));

  try {
    // 方法1: 移除包含data-shape="custom-pin"的完整g元素
    const beforeMethod1 = processedSvg.length;
    processedSvg = processedSvg.replace(/<g[^>]*data-shape="custom-pin"[^>]*>[\s\S]*?<\/g>/g, '');
    console.log('方法1移除后长度变化:', beforeMethod1 - processedSvg.length);

    // 方法2: 如果方法1没有效果，尝试更宽泛的匹配
    if (processedSvg.includes('custom-pin')) {
      console.log('方法1未完全移除，尝试方法2');
      const beforeMethod2 = processedSvg.length;
      // 匹配任何包含custom-pin的g元素
      processedSvg = processedSvg.replace(/<g[^>]*custom-pin[^>]*>[\s\S]*?<\/g>/gi, '');
      console.log('方法2移除后长度变化:', beforeMethod2 - processedSvg.length);
    }

    // 方法3: 如果还有残留，直接移除包含custom-pin的任何元素
    if (processedSvg.includes('custom-pin')) {
      console.log('方法2未完全移除，尝试方法3');
      const beforeMethod3 = processedSvg.length;
      // 移除任何包含custom-pin的标签
      processedSvg = processedSvg.replace(/<[^>]*custom-pin[^>]*>[\s\S]*?<\/[^>]*>/gi, '');
      // 移除可能的单标签
      processedSvg = processedSvg.replace(/<[^>]*custom-pin[^>]*\/>/gi, '');
      console.log('方法3移除后长度变化:', beforeMethod3 - processedSvg.length);
    }

    // 方法4: 最后的清理 - 移除任何包含custom-pin的文本
    if (processedSvg.includes('custom-pin')) {
      console.log('方法3未完全移除，尝试方法4');
      const beforeMethod4 = processedSvg.length;
      // 移除包含custom-pin的任何内容
      processedSvg = processedSvg.replace(/custom-pin/g, '');
      console.log('方法4移除后长度变化:', beforeMethod4 - processedSvg.length);
    }

    console.log('移除后是否包含custom-pin:', processedSvg.includes('custom-pin'));

    // 清理可能残留的空白g元素
    const beforeCleanup = processedSvg.length;
    processedSvg = processedSvg.replace(/<g[^>]*>\s*<\/g>/g, '');
    console.log('清理空白g元素后长度变化:', beforeCleanup - processedSvg.length);

    // 标准化ViewBox，消除留白
    const beforeNormalize = processedSvg.length;
    processedSvg = normalizeViewBox(processedSvg);
    console.log('标准化ViewBox后长度变化:', beforeNormalize - processedSvg.length);

    console.log('最终处理后的SVG长度:', processedSvg.length);

  } catch (error) {
    console.error('移除引脚时发生错误:', error);
    // 如果出错，返回原始数据
    return svgData;
  }

  return processedSvg;
};

// // 查找SVG内容的实际边界
// const findContentBounds = (svgData: string) => {
//   const bounds = { minX: Infinity, minY: Infinity, maxX: -Infinity, maxY: -Infinity };

//   // 辅助函数：更新边界
//   const updateBounds = (x: number, y: number, width: number = 0, height: number = 0, radius: number = 0) => {
//     if (radius > 0) {
//       bounds.minX = Math.min(bounds.minX, x - radius);
//       bounds.minY = Math.min(bounds.minY, y - radius);
//       bounds.maxX = Math.max(bounds.maxX, x + radius);
//       bounds.maxY = Math.max(bounds.maxY, y + radius);
//     } else {
//       bounds.minX = Math.min(bounds.minX, x);
//       bounds.minY = Math.min(bounds.minY, y);
//       bounds.maxX = Math.max(bounds.maxX, x + width);
//       bounds.maxY = Math.max(bounds.maxY, y + height);
//     }
//   };

//   // 1. 查找所有有transform的元素
//   const transformRegex = /transform="translate\(([^,]+),([^)]+)\)"/g;
//   let match;

//   while ((match = transformRegex.exec(svgData)) !== null) {
//     const x = parseFloat(match[1]);
//     const y = parseFloat(match[2]);

//     // 查找该元素的尺寸
//     const elementStart = match.index;
//     const elementEnd = svgData.indexOf('</g>', elementStart);
//     const elementContent = svgData.substring(elementStart, elementEnd);

//     // 查找矩形尺寸
//     const rectMatch = elementContent.match(/<rect[^>]*width="([^"]+)"[^>]*height="([^"]+)"/);
//     if (rectMatch) {
//       const width = parseFloat(rectMatch[1]);
//       const height = parseFloat(rectMatch[2]);
//       updateBounds(x, y, width, height);
//     }

//     // 查找圆形尺寸
//     const circleMatch = elementContent.match(/<circle[^>]*r="([^"]+)"/);
//     if (circleMatch) {
//       const r = parseFloat(circleMatch[1]);
//       updateBounds(x, y, 0, 0, r);
//     }

//     // 查找椭圆尺寸
//     const ellipseMatch = elementContent.match(/<ellipse[^>]*rx="([^"]+)"[^>]*ry="([^"]+)"/);
//     if (ellipseMatch) {
//       const rx = parseFloat(ellipseMatch[1]);
//       const ry = parseFloat(ellipseMatch[2]);
//       // 椭圆用最大半径作为近似
//       const maxRadius = Math.max(rx, ry);
//       updateBounds(x, y, 0, 0, maxRadius);
//     }

//     // 查找多边形尺寸
//     const polygonMatch = elementContent.match(/<polygon[^>]*points="([^"]+)"/);
//     if (polygonMatch) {
//       const points = polygonMatch[1].split(/[\s,]+/).map(Number);
//       for (let i = 0; i < points.length; i += 2) {
//         if (i + 1 < points.length) {
//           const px = x + points[i];
//           const py = y + points[i + 1];
//           updateBounds(px, py);
//         }
//       }
//     }

//     // 查找路径元素（简单处理）
//     const pathMatch = elementContent.match(/<path[^>]*d="([^"]+)"/);
//     if (pathMatch) {
//       // 对于路径，假设在合理范围内
//       updateBounds(x, y, 100, 100);
//     }
//   }

//   // 2. 查找没有transform的直接定位元素
//   // 矩形元素
//   const directRectRegex = /<rect[^>]*x="([^"]+)"[^>]*y="([^"]+)"[^>]*width="([^"]+)"[^>]*height="([^"]+)"/g;
//   while ((match = directRectRegex.exec(svgData)) !== null) {
//     const x = parseFloat(match[1]);
//     const y = parseFloat(match[2]);
//     const width = parseFloat(match[3]);
//     const height = parseFloat(match[4]);
//     updateBounds(x, y, width, height);
//   }

//   // 圆形元素
//   const directCircleRegex = /<circle[^>]*cx="([^"]+)"[^>]*cy="([^"]+)"[^>]*r="([^"]+)"/g;
//   while ((match = directCircleRegex.exec(svgData)) !== null) {
//     const cx = parseFloat(match[1]);
//     const cy = parseFloat(match[2]);
//     const r = parseFloat(match[3]);
//     updateBounds(cx, cy, 0, 0, r);
//   }

//   // 椭圆元素
//   const directEllipseRegex = /<ellipse[^>]*cx="([^"]+)"[^>]*cy="([^"]+)"[^>]*rx="([^"]+)"[^>]*ry="([^"]+)"/g;
//   while ((match = directEllipseRegex.exec(svgData)) !== null) {
//     const cx = parseFloat(match[1]);
//     const cy = parseFloat(match[2]);
//     const rx = parseFloat(match[3]);
//     const ry = parseFloat(match[4]);
//     const maxRadius = Math.max(rx, ry);
//     updateBounds(cx, cy, 0, 0, maxRadius);
//   }

//   // 如果没有找到内容，返回默认值
//   if (bounds.minX === Infinity) {
//     return { minX: 0, minY: 0, width: 100, height: 60 };
//   }

//   return {
//     minX: bounds.minX,
//     minY: bounds.minY,
//     width: bounds.maxX - bounds.minX,
//     height: bounds.maxY - bounds.minY
//   };
// };

// 设置ViewBox为自适应模式
const normalizeViewBox = (svgData: string) => {
  try {
    console.log('开始标准化ViewBox，SVG长度:', svgData.length);

    // 检查是否存在viewBox
    const viewBoxMatch = svgData.match(/viewBox="([^"]*)"/i);

    if (viewBoxMatch) {
      console.log(`找到现有的viewBox: ${viewBoxMatch[1]}`);

      // 检查viewBox是否包含百分比值
      if (viewBoxMatch[1].includes('%')) {
        console.log('ViewBox包含百分比值，移除viewBox让SVG使用原始尺寸');
        // 移除现有的viewBox，让SVG使用原始尺寸
        const result = svgData.replace(/viewBox="[^"]*"\s*/, '');
        console.log('已移除包含百分比的viewBox');
        return result;
      } else {
        console.log('ViewBox不包含百分比，保持原样');
        return svgData;
      }
    } else {
      console.log('没有找到viewBox，保持原样');
      return svgData;
    }
  } catch (error) {
    console.error('标准化ViewBox时发生错误:', error);
    // 如果出错，返回原始数据
    return svgData;
  }
};

// 新增：动态注册JSON组件的函数
export const registerDynamicJsonComponent = (componentId: string, componentInfo: any, options: any = {}) => {
  try {
    console.log(`开始注册动态JSON组合组件: ${componentId}`);
    console.log('组件信息:', componentInfo);

    const {
      width = 200,
      height = 150,
      name = componentInfo.name || `组合组件${componentId}`
    } = options;

    // 从componentInfo中提取引脚信息
    const externalPins = componentInfo.pins || [];
    const internalNodes = componentInfo.nodes || [];
    const internalEdges = componentInfo.edges || [];

    console.log(`组合组件包含: ${internalNodes.length}个节点, ${internalEdges.length}条边, ${externalPins.length}个外部引脚`);

    // 生成外部引脚的ports配置
    const dynamicPorts = externalPins.map((pin: any, index: number) => {
      // 计算引脚在组件边界上的位置
      let finalX, finalY;

      // 根据引脚的原始位置和类型确定在边界上的位置
      const relativeX = pin.position ? pin.position.x / width : 0;
      // const relativeY = pin.position ? pin.position.y / height : 0;

      // 根据引脚类型或位置确定应该在哪个边
      if (pin.type === 'input' || relativeX <= 0.3) {
        // 输入引脚放在左边
        finalX = 0;
        finalY = Math.max(10, Math.min((index + 1) * (height / (externalPins.length + 1)), height - 10));
      } else if (pin.type === 'output' || relativeX >= 0.7) {
        // 输出引脚放在右边
        finalX = width;
        finalY = Math.max(10, Math.min((index + 1) * (height / (externalPins.length + 1)), height - 10));
      } else {
        // 其他引脚根据原始位置放置
        finalX = Math.max(5, Math.min(pin.position?.x || width / 2, width - 5));
        finalY = Math.max(5, Math.min(pin.position?.y || height / 2, height - 5));
      }

      console.log(`组合组件引脚 ${pin.label || pin.id} 位置: (${Math.round(finalX)}, ${Math.round(finalY)})`);

      return {
        id: pin.id || `pin_${index}`,
        group: 'absolute',
        args: {
          x: Math.round(finalX),
          y: Math.round(finalY)
        },
        // 保存引脚的原始信息，用于后续的连接映射
        pinInfo: pin
      };
    });

    // 注册为X6 Group节点，而不是普通节点
    Graph.registerNode(
      componentId,
      {
        inherit: 'group', // 继承group节点
        width: width,
        height: height,
        attrs: {
          body: {
            stroke: '#5F95FF',
            strokeWidth: 2,
            fill: '#EFF4FF',
            fillOpacity: 0.1,
            rx: 8,
            ry: 8,
          },
          label: {
            text: name,
            fontSize: 12,
            fill: '#333',
            textAnchor: 'middle',
            textVerticalAnchor: 'top',
            refY: 5,
          },
        },
        // 保存组合组件的完整信息
        data: {
          componentInfo: componentInfo,
          componentName: name,
          renderType: 'json-group',
          internalNodes: internalNodes,
          internalEdges: internalEdges,
          externalPins: externalPins,
          // 创建时需要恢复内部结构的标志
          needsInternalStructure: true
        },
        ports: {
          groups: {
            absolute: {
              position: 'absolute',
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: '#31d0c6',
                  strokeWidth: 2,
                  fill: '#fff',
                  style: {
                    visibility: 'visible',
                    cursor: 'crosshair',
                  },
                },
              },
            },
          },
          items: dynamicPorts,
        },
      },
      true // 覆盖同名节点
    );

    console.log(`✅ JSON组合组件 ${componentId} 注册成功`);
    console.log(`外部引脚数量: ${dynamicPorts.length}`);

    return {
      componentId,
      componentInfo,
      ports: dynamicPorts,
      nodeType: 'group'
    };

  } catch (error) {
    console.error(`❌ 注册JSON组合组件 ${componentId} 失败:`, error);
    return null;
  }
};

// 动态注册SVG元件的函数
export const registerDynamicSvg = (svgId: string, svgData: string, options: any = {}) => {
  try {
    console.log(`开始注册动态SVG组件: ${svgId}`);
    console.log('原始SVG数据长度:', svgData.length);
    console.log('原始SVG数据前100字符:', svgData.substring(0, 100));

    // 预处理SVG数据
    const preprocessedSvg = preprocessSvg(svgData);
    console.log('预处理后SVG数据长度:', preprocessedSvg.length);
    console.log('预处理后SVG数据前100字符:', preprocessedSvg.substring(0, 100));

    const {
      width = 120,
      height = 80,
      name = `动态元件${svgId}`,
      // ports = []
    } = options;

    // 第一步：解析SVG中的引脚信息
    console.log('第一步：解析引脚信息');
    const parseResult = parsePinsFromSvg(preprocessedSvg);
    console.log('解析出的引脚信息:', parseResult.pins);

    // 第二步：移除SVG中的引脚元素
    console.log('第二步：移除引脚元素');
    const processedSvgData = removePinsFromSvg(preprocessedSvg);
    console.log('原始SVG长度:', preprocessedSvg.length);
    console.log('处理后SVG长度:', processedSvgData.length);
    console.log('处理后SVG前100字符:', processedSvgData.substring(0, 100));

    // 第三步：根据解析出的引脚信息生成ports配置
    console.log('第三步：生成ports配置');
    const dynamicPorts = parseResult.pins.map((pin, index) => {
      let finalX, finalY;

      // 根据引脚所在的边和相对位置计算最终位置
      switch (pin.side) {
        case 'left':
          finalX = 5; // 左边缘
          finalY = pin.relativeY * height; // 使用相对Y位置
          break;
        case 'right':
          finalX = width - 5; // 右边缘
          finalY = pin.relativeY * height; // 使用相对Y位置
          break;
        case 'top':
          finalX = pin.relativeX * width; // 使用相对X位置
          finalY = 5; // 上边缘
          break;
        case 'bottom':
          finalX = pin.relativeX * width; // 使用相对X位置
          finalY = height - 5; // 下边缘
          break;
        default:
          // 如果无法确定边，放在中心
          finalX = width / 2;
          finalY = height / 2;
      }

      // 确保位置在合理范围内
      finalX = Math.max(5, Math.min(finalX, width - 5));
      finalY = Math.max(5, Math.min(finalY, height - 5));

      console.log(`引脚 ${index} 最终位置: (${Math.round(finalX)}, ${Math.round(finalY)}) - 边: ${pin.side}`);

      return {
        id: pin.id,
        group: 'absolute',
        args: {
          x: Math.round(finalX),
          y: Math.round(finalY)
        },
        // 调试信息
        debug: {
          side: pin.side,
          relativePos: { x: pin.relativeX, y: pin.relativeY },
          originalCenter: { x: pin.centerX, y: pin.centerY }
        }
      };
    });

    // 如果没有解析到引脚，就不设置引脚
    const finalPorts = dynamicPorts.length > 0 ? dynamicPorts : [];

    // 添加到 custom 对象
    (jcCustom as any)[svgId] = jcCustom.dynamicSvg;

    // 注册形状 - 使用与其他电气元件相同的方式
    register({
      shape: svgId,  // 使用唯一的ID作为shape名称
      width: width,
      height: height,
      data: {
        width: width,
        height: height,
        svgData: processedSvgData, // 保存处理后的SVG数据（引脚被移除）
        componentName: name,
        originalPins: parseResult.pins // 保存原始引脚信息用于调试
      },
      component: jcCustom.dynamicSvg,
      ports: {
        groups: {
          absolute: {
            position: 'absolute',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: { visibility: 'visible', cursor: 'crosshair' },
              },
            },
          },
        },
        items: finalPorts,
      },
    });

    console.log(`✅ 动态注册SVG组件成功: ${svgId} (${name})`);
    console.log(`引脚数量: ${dynamicPorts.length}`);
    console.log(`最终引脚配置:`, finalPorts);

    // 返回处理结果，供调用方使用
    return {
      processedSvgData,
      pins: parseResult.pins,
      ports: finalPorts
    };

  } catch (error) {
    console.error(`❌ 注册动态SVG组件失败: ${svgId}`, error);
    console.error('错误详情:', error);
    console.error('SVG数据长度:', svgData.length);
    console.error('SVG数据前200字符:', svgData.substring(0, 200));

    // 返回错误信息，但不抛出异常，避免影响其他组件
    return {
      processedSvgData: svgData, // 返回原始数据
      pins: [],
      ports: [],
      error: error instanceof Error ? error.message : String(error)
    };
  }
};

export default registerInit;
