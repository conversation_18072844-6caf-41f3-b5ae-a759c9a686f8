<template>
  <div class="material-container">
    <!-- 主菜单栏 -->
    <div class="main-menu">
      <div
        v-for="(menu, idx) in mainMenus"
        :key="menu.name"
        :class="['main-menu-item', { active: idx === activeMainMenu }]"
        @click="activeMainMenu = idx; activeSubMenu = 0"
      >
        <el-icon>
          <img :src="menu.icon" class="menu-icon-img" alt="" />       
        </el-icon>
        <span class="main-menu-text">{{ menu.name }}</span>
      </div>
    </div>
    <!-- 子菜单栏 :disabled="!sub.remark"-->
    <div class="sub-menu">
      <div class="sub-menu-list">
        <el-tooltip
          v-for="(sub, idx) in mainMenus[activeMainMenu].subMenus"
          :key="sub.id"
          :content="sub.remark || '暂无备注信息'"
          placement="bottom"
          effect="light"
          :show-arrow="true"
          :offset="8"
          :disabled="!sub.remark"
          popper-class="custom-tooltip"
        >
          <div
            :class="['sub-menu-item', { active: idx === activeSubMenu }]"
            @click="activeSubMenu = idx"
            @contextmenu.prevent="handleRightClick($event, sub, idx)"
          >
            {{ sub.name }}
          </div>
        </el-tooltip>
      </div>
      <el-button class="add-group-btn" type="primary" plain @click="showCreateGroupDialog">+ 新建分组</el-button>
    </div>
    <!-- 内容区 -->
    <div class="content-area">
      <div class="main-header">
        <h2>素材中心</h2>
        <div>
          <el-button type="primary">+ 上传元件</el-button>
          <el-button @click="() => { isEditMode = false; editData = null; dialogVisible = true; }">新建元件</el-button>
        </div>
      </div>
      <div class="material-grid">
        <el-card v-for="component in components" :key="component.id" class="material-card">
          <div class="card-content" @click="handleCardClick(component.id)">
            <div class="card-image"></div>
            <div class="card-info">
              <div class="card-title-row">
                <h4 class="card-title">{{ component.name }}</h4>
                <el-tag>草稿件</el-tag>
              </div>
              <p class="card-description">{{ component.description }}</p>
            </div>
          </div>
          <div class="card-actions">
            <el-button link type="primary" @click="handleEditComponent(component.id)">编辑</el-button>
            <el-divider direction="vertical" />
            <el-button link type="primary" @click="handleDeleteComponent(component.id)">删除</el-button>
          </div>
        </el-card>
      </div>
      <create-component-dialog 
        v-model:visible="dialogVisible" 
        :edit-data="editData" 
        :is-edit="isEditMode" 
        @create-success="handleCreateSuccess" 
        @edit-success="handleEditSuccess" 
      />
      <create-group-dialog 
        v-model:visible="groupDialogVisible" 
        @success="handleCreateGroupSuccess" 
      />
    </div>
    
    <!-- 右键菜单 -->
    <div 
      v-show="contextMenuVisible" 
      :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }"
      class="context-menu"
      @click.stop
    >
      <div class="context-menu-item" @click="handleEditType">
        <el-icon><Edit /></el-icon>
        <span>编辑</span>
      </div>
      <div class="context-menu-item" @click="handleDeleteType">
        <el-icon><Delete /></el-icon>
        <span>删除</span>
      </div>
    </div>
    
    <!-- 编辑分组对话框 -->
    <el-dialog 
      v-model="editTypeDialogVisible" 
      title="编辑分组" 
      width="400px"
    >
      <el-form :model="editTypeForm" label-width="80px">
        <el-form-item label="分组名称">
          <el-input v-model="editTypeForm.name" placeholder="请输入分组名称" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input 
            v-model="editTypeForm.remark" 
            type="textarea" 
            placeholder="请输入备注信息" 
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editTypeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmEditType">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, shallowRef, onMounted, computed, watch, onBeforeUnmount } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
import { useComponentStore, useComponentTypeStore } from '@/store';
import { getComponentList, deleteComponent, getComponent, deleteComponentType, updateComponentType } from '@/api/index';
import type { Component } from '@/api/types';
import CreateComponentDialog from './CreateComponentDialog.vue';
import CreateGroupDialog from './CreateGroupDialog.vue';
import slice7Icon from '@/assets/images/icon/slice_7.png';
import slice6Icon from '@/assets/images/icon/slice_6.png';
import slice5Icon from '@/assets/images/icon/slice_5.png';
import { Edit, Delete } from '@element-plus/icons-vue';

const router = useRouter();
const route = useRoute();
const componentStore = useComponentStore();
const componentTypeStore = useComponentTypeStore();

const components = ref<Component[]>([]);
const loading = ref(false);

interface SubMenuItem {
  id: number | string;
  name: string;
  description: string;
  remark?: string;
}

const mainMenus = shallowRef<{ name: string; icon: string; subMenus: SubMenuItem[] }[]>([
  {
    name: '元件定义',
    icon: slice7Icon,
    subMenus: []
  },
  {
    name: '子系统素材',
    icon: slice6Icon,
    subMenus: ['子系统A', '子系统B', '子系统C'].map(name => ({ id: name, name, description: '' }))
  },
  {
    name: '控制系统素材',
    icon: slice5Icon,
    subMenus: ['控制A', '控制B', '控制C'].map(name => ({ id: name, name, description: '' }))
  }
]);

const activeMainMenu = ref(0);
const activeSubMenu = ref(0);
const dialogVisible = ref(false);
const editData = ref<Component | null>(null);
const isEditMode = ref(false);
const groupDialogVisible = ref(false);

// 右键菜单相关
const contextMenuVisible = ref(false);
const contextMenuX = ref(0);
const contextMenuY = ref(0);
const currentRightClickItem = ref<SubMenuItem | null>(null);
const currentRightClickIndex = ref<number>(-1);

// 编辑分组对话框
const editTypeDialogVisible = ref(false);
const editTypeForm = ref({
  id: 0,
  name: '',
  remark: ''
});

const activeComponentTypeId = computed(() => {
  if (activeMainMenu.value === 0 && mainMenus.value[0]?.subMenus[activeSubMenu.value]) {
    return mainMenus.value[0].subMenus[activeSubMenu.value].id;
  }
  return null;
});

const fetchMaterialComponents = async (typeId: number | string | null) => {
  if (typeId === null) {
    components.value = [];
    return;
  }
  loading.value = true;
  try {
    const response = await getComponentList({ component_type_id: Number(typeId) });
    console.log('----------response',response)
   
    if (response.code === 200) {
      components.value = response.data || [];
    } else {
      console.error('获取元件列表失败:', response.message);
      components.value = [];
    }
  } catch (error) {
    console.error('获取元件列表失败:', error);
    components.value = [];
  } finally {
    loading.value = false;
  }
};


// 监听当前选中的分组ID，变化时重新获取列表
watch(activeComponentTypeId, (newId) => {
  fetchMaterialComponents(newId);
});

const handleCreateSuccess = () => {
  if (activeComponentTypeId.value) {
    fetchMaterialComponents(activeComponentTypeId.value);
  }
};

const handleEditSuccess = () => {
  if (activeComponentTypeId.value) {
    fetchMaterialComponents(activeComponentTypeId.value);
  }
};

const handleCardClick = (id: string) => {
  router.push(`/component-editor/${id}`);
};

const handleEditComponent = async (id: string) => {
  try {
    // 获取元件详情
    const response = await getComponent({ component_id: id });
    if (response.code === 200) {
      // 设置编辑数据并打开对话框
      editData.value = response.data;
      isEditMode.value = true;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取元件信息失败');
    }
  } catch (error) {
    console.error('获取元件信息失败:', error);
    ElMessage.error('获取元件信息失败');
  }
};

const handleDeleteComponent = async (id: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个元件吗？删除后无法恢复。',
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );
    
    // 调用删除接口
    const response = await deleteComponent({ component_id: id });
    if (response.code === 200) {
      ElMessage.success('删除成功');
      // 重新获取列表
      if (activeComponentTypeId.value) {
        fetchMaterialComponents(activeComponentTypeId.value);
      }
    } else {
      ElMessage.error(response.message || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除元件失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

const showCreateGroupDialog = () => {
  groupDialogVisible.value = true;
};

const handleCreateGroupSuccess = async () => {
  console.log('重新获取元件类型数据')
  await componentTypeStore.fetchComponentTypes();
  if (componentTypeStore.types.length > 0) {
    const newMenus = [...mainMenus.value];
    newMenus[0] = { ...newMenus[0], subMenus: componentTypeStore.types };
    mainMenus.value = newMenus;
  }
};

// 右键菜单处理
const handleRightClick = (event: MouseEvent, item: SubMenuItem, index: number) => {
  // 只对元件定义分组显示右键菜单
  if (activeMainMenu.value !== 0) return;
  
  // 当is_origin为1时不显示右键菜单
  if ((item as any).is_origin === 1) return;
  
  event.preventDefault();
  contextMenuX.value = event.clientX;
  contextMenuY.value = event.clientY;
  currentRightClickItem.value = item;
  currentRightClickIndex.value = index;
  contextMenuVisible.value = true;
};

// 隐藏右键菜单
const hideContextMenu = () => {
  contextMenuVisible.value = false;
};

// 编辑分组
const handleEditType = () => {
  if (currentRightClickItem.value) {
    editTypeForm.value = {
      id: Number(currentRightClickItem.value.id),
      name: currentRightClickItem.value.name,
      remark: currentRightClickItem.value.remark || ''
    };
    editTypeDialogVisible.value = true;
  }
  hideContextMenu();
};

// 确认编辑分组
const confirmEditType = async () => {
  try {
    const response = await updateComponentType(editTypeForm.value);
    if (response.code === 200) {
      ElMessage.success('编辑成功');
      editTypeDialogVisible.value = false;
      await handleCreateGroupSuccess(); // 重新获取数据
    } else {
      ElMessage.error(response.message || '编辑失败');
    }
  } catch (error) {
    console.error('编辑分组失败:', error);
    ElMessage.error('编辑失败');
  }
};

// 删除分组
const handleDeleteType = async () => {
  if (!currentRightClickItem.value) return;
  
  try {
    await ElMessageBox.confirm(
      `确定要删除分组 "${currentRightClickItem.value.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    const response = await deleteComponentType({ id: Number(currentRightClickItem.value.id) });
    if (response.code === 200) {
      ElMessage.success('删除成功');
      await handleCreateGroupSuccess(); // 重新获取数据
      // 如果删除的是当前选中的分组，重置选中状态
      if (currentRightClickIndex.value === activeSubMenu.value) {
        activeSubMenu.value = 0;
      }
    } else {
      ElMessage.error(response.message || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分组失败:', error);
      ElMessage.error('删除失败');
    }
  }
  hideContextMenu();
};

// 点击其他地方隐藏右键菜单
const handleDocumentClick = () => {
  hideContextMenu();
};

// 组件挂载时初始化数据和事件监听
onMounted(async () => {
  // 添加全局点击事件监听
  document.addEventListener('click', handleDocumentClick);
  
  // 获取元件类型数据
  await componentTypeStore.fetchComponentTypes();
  if (componentTypeStore.types.length > 0) {
    const newMenus = [...mainMenus.value];
    newMenus[0] = { ...newMenus[0], subMenus: componentTypeStore.types };
    mainMenus.value = newMenus;
    
    // 检查是否有component_type_id查询参数
    const componentTypeId = route.query.component_type_id;
    if (componentTypeId) {
      // 找到对应的分组索引并设置为活跃状态
      const targetIndex = componentTypeStore.types.findIndex(
        type => type.id.toString() === componentTypeId.toString()
      );
      if (targetIndex !== -1) {
        activeMainMenu.value = 0; // 设置为元件定义
        activeSubMenu.value = targetIndex; // 设置为对应的分组
      }
    }
    
    // 默认获取第一个分组的元件
    if(activeComponentTypeId.value) {
      fetchMaterialComponents(activeComponentTypeId.value);
    }
  }
});

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('click', handleDocumentClick);
});
</script>

<style scoped>
.material-container {
  display: flex;
  height: 100vh;
  background: #f7f9fb;
}
.main-menu {
  background: #f4f7fa;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  padding-top: 0px;
  border-right: 2px solid #e0e6ed;
  z-index: 2;
}
.main-menu-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  height: 48px;
  padding: 0 8px;
  font-size: 14px;
  color: #6b7b8d;
  cursor: pointer;
  border-radius: 8px 0 0 8px;
  margin-bottom: 8px;
  transition: background 0.2s, color 0.2s;
  font-weight: 500;
}
.main-menu-item .el-icon {
  font-size: 20px;
  margin-right: 4px;
}
.menu-icon-img {
  width: 20px;
  height: 20px;
}
.main-menu-item.active {
  background: #409eff;
  color: #fff;
  font-weight: bold;
}
.main-menu-item.active .el-icon {
  color: #fff;
}
.main-menu-text {
  flex: 1;
  margin-left: 4px;
  font-size: 14px;
}
.sub-menu {
  width: 160px;
  background: #fff;
  border-right: 2px solid #e0e6ed;
  display: flex;
  flex-direction: column;
  padding:0;
  z-index: 1;
  height: 100vh;
}
.sub-menu-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  max-height: calc(100vh - 140px); /* 为按钮和padding预留空间 */
}

/* 自定义滚动条样式 - 默认隐藏，悬停时显示 */
.sub-menu-list::-webkit-scrollbar {
  width: 6px;
}

.sub-menu-list::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.sub-menu-list::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 3px;
}

.sub-menu-list:hover::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.sub-menu-list:hover::-webkit-scrollbar-thumb {
  background: #c1c1c1;
}

.sub-menu-list:hover::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
.sub-menu-item {
  height: 40px;
  padding: 4px 24px;
  cursor: pointer;
  color: #6b7b8d;
  border-left: 3px solid transparent;
  font-size: 15px;
  transition: background 0.2s, color 0.2s, border-color 0.2s;
  border-radius: 0 8px 8px 0;
  display: flex;
  align-items: center;
}
.sub-menu-item.active {
  color: #409eff;
  background: #e6f0ff;
  border-left: 3px solid #409eff;
  font-weight: bold;
}
.add-group-btn {
  margin: 20px 24px 0 24px;
  flex-shrink: 0;
}
.content-area {
  flex: 1;
  padding: 32px 32px 0 32px;
  overflow-y: auto;
}
.main-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.material-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.material-card {
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e0e6ed;
  padding: 16px;
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.3s, border-color 0.3s;
}

.material-card:hover {
  border-color: #cce1ff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-content {
  display: flex;
  gap: 16px;
  flex: 1;
  cursor: pointer;
  transition: background-color 0.2s;
}

.card-content:hover {
  background-color: #f8f9fa;
  border-radius: 4px;
}
.card-image {
  width: 80px;
  height: 80px;
  background-color: #f0f2f5;
  border-radius: 4px;
  flex-shrink: 0;
}
.card-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}
.card-title-row {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
}
.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0px;
  margin: 0px;
}
.card-info p.card-description {
  font-size: 13px;
  color: #888;
  margin: 0 0 10px 0;
  flex: 1;
  line-height: 1.5;
  /* 显示两行，超出部分省略号 */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.card-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f2f5;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
</style>

<style>
/* 自定义tooltip样式 */
.custom-tooltip {
 
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  padding: 8px 12px !important;
  max-width: 200px !important;
  line-height: 1.4 !important;
}

/*.custom-tooltip .el-popper__arrow::before {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: 1px solid #5a67d8 !important;
}
*/
/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  min-width: 120px;
  padding: 4px 0;
}

.context-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.context-menu-item .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.context-menu-item:first-child:hover {
  color: #409eff;
}

.context-menu-item:last-child:hover {
  color: #f56c6c;
}
</style>