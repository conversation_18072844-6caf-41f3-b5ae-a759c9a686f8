<template>
  <el-dialog
    v-model="dialogVisible"
    title="新建分组"
    width="500px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="80px"
      label-position="left"
    >
      <el-form-item label="分组名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入..."
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="备注说明" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          placeholder="请输入..."
          :rows="4"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { addComponentType } from '@/api/index';

interface Props {
  visible: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const loading = ref(false);

const dialogVisible = ref(false);

const formData = reactive({
  name: '',
  remark: ''
});

const rules: FormRules = {
  name: [
    { required: true, message: '请输入分组名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
};

// 监听外部传入的visible变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal) {
      // 重置表单
      resetForm();
    }
  },
  { immediate: true }
);

// 监听内部dialogVisible变化，同步到外部
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal);
});

const resetForm = () => {
  formData.name = '';
  formData.remark = '';
  formRef.value?.clearValidate();
};

const handleClose = () => {
  dialogVisible.value = false;
};

const handleConfirm = async () => {
  if (!formRef.value) return;
  
  try {
    const valid = await formRef.value.validate();
    if (!valid) return;
    
    loading.value = true;
    
    const response = await addComponentType({
      name: formData.name,
      remark: formData.remark
    });
    
    if (response.code === 200) {
      ElMessage.success('新建成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(response.message || '新建失败');
    }
  } catch (error) {
    console.error('新建分组失败:', error);
    ElMessage.error('新建失败');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>