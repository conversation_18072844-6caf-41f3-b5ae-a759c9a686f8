<template>
    <el-dialog v-model="visible_" :title="isEdit ? '编辑元件' : '新建元件'" width="800px" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="true">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="元件名称" prop="name" required>
              <el-input v-model="form.name" placeholder="请输入..." />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关键字" prop="keyword" required>
              <el-input v-model="form.keyword" placeholder="请输入..." />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="元件分组" prop="component_type_id" required>
              <el-select v-model="form.component_type_id" placeholder="请选择分组">
                <el-option v-for="item in componentTypeStore.types" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 暂时注释掉不用的字段 -->
          <!-- <el-col :span="12">
            <el-form-item label="元件版本" required>
              <el-input v-model="form.version" placeholder="请输入..." />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="元件图片">
              <el-popover placement="bottom" title="选择图标" :width="400" trigger="click">
                <template #reference>
                  <el-button>{{ form.image ? '已选择' : '选择图标' }}</el-button>
                </template>
                <div class="icon-list">
                  <div v-for="icon in icons" :key="icon" class="icon-item" @click="form.image = icon">
                    <img :src="getImageUrl(icon)" />
                  </div>
                </div>
              </el-popover>
              <img v-if="form.image" :src="getImageUrl(form.image)" class="selected-icon" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="展示使用">
              <el-checkbox-group v-model="form.displayType">
                <el-checkbox label="image">图片</el-checkbox>
                <el-checkbox label="draw">绘制</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col> -->
          <el-col :span="24">
            <el-form-item label="元件说明" prop="description">
              <el-input v-model="form.description" type="textarea" placeholder="请输入..." />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" :loading="loading" @click="confirm">{{ isEdit ? '保存' : '确认' }}</el-button>
      </template>
    </el-dialog>
  </template>
  
  <script setup lang="ts">
import { ref, watch, defineProps, defineEmits, reactive, onMounted, computed } from 'vue';
import { ElMessage, type FormInstance } from 'element-plus';
import { useComponentTypeStore } from '@/store';
import { addComponent, updateComponent } from '@/api';

const props = defineProps<{ 
  visible: boolean;
  editData?: any;
  isEdit?: boolean;
}>();
const emit = defineEmits(['update:visible', 'create-success', 'edit-success']);
const visible_ = ref(props.visible);
const componentTypeStore = useComponentTypeStore();
const formRef = ref<FormInstance>();
const loading = ref(false);
const isEdit = computed(() => props.isEdit);

const form = reactive({
  name: '',
  keyword: '',
  component_type_id: '',
  description: '',
  // 注释掉暂时不用的字段
  // version: '',
  // image: '',
  // displayType: [],
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入元件名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  keyword: [
    { required: true, message: '请输入关键字', trigger: 'blur' },
    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  component_type_id: [
    { required: true, message: '请选择元件分组', trigger: 'change' }
  ],
  description: [
    { max: 500, message: '描述不能超过 500 个字符', trigger: 'blur' }
  ]
};

onMounted(() => {
  componentTypeStore.fetchComponentTypes();
});

watch(() => props.visible, v => {
  visible_.value = v;
  if (v && props.isEdit && props.editData) {
    // 编辑模式下回显数据
    Object.assign(form, {
      name: props.editData.name || '',
      keyword: props.editData.keyword || '',
      component_type_id: props.editData.component_type_id || '',
      description: props.editData.description || ''
    });
  }
});
watch(visible_, v => emit('update:visible', v));

function close() { 
  visible_.value = false;
  resetForm();
}

function resetForm() {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置表单数据
  Object.assign(form, {
    name: '',
    keyword: '',
    component_type_id: '',
    description: ''
  });
}

async function confirm() {
  if (!formRef.value) return;
  
  try {
    // 表单验证
    await formRef.value.validate();
    
    loading.value = true;
    
    if (props.isEdit) {
       // 编辑模式
       const response = await updateComponent({
         component_id: props.editData.id,
         name: form.name,
         keyword: form.keyword,
         component_type_id: parseInt(form.component_type_id),
         description: form.description || undefined
       });
       ElMessage.success('元件编辑成功');
       emit('edit-success');
    } else {
      // 新建模式
      const response = await addComponent({
        name: form.name,
        keyword: form.keyword,
        component_type_id: parseInt(form.component_type_id),
        description: form.description || undefined
      });
      ElMessage.success('元件创建成功');
      emit('create-success');
    }
    
    close();
    
  } catch (error: any) {
    // 验证失败时，validate promise会reject，但没有错误信息，这里可以加个判断避免不必要的报错
    if (error) {
      console.error(props.isEdit ? '编辑元件失败:' : '创建元件失败:', error);
      //ElMessage.error(error.message || (props.isEdit ? '编辑元件失败' : '创建元件失败'));
    }
  } finally {
    loading.value = false;
  }
}
</script>
  
  <style scoped>
.el-dialog {
  border-radius: 16px;
}
.icon-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
}
.icon-item {
  width: 50px;
  height: 50px;
  cursor: pointer;
  border: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-item img {
  max-width: 100%;
  max-height: 100%;
}
.selected-icon {
  width: 32px;
  height: 32px;
  margin-left: 10px;
}
</style>
  