import { Graph, Node } from '@antv/x6';

// 点和线段的基础接口
interface Point {
  x: number;
  y: number;
}

interface LineSegment {
  start: Point;
  end: Point;
}

interface PolygonEdge extends LineSegment {
  type: 'horizontal' | 'vertical' | 'diagonal';
  length: number;
}

/**
 * 引脚自动吸附管理器 - ComponentEditor专用版本
 * 从advancedDragPortManager.ts提取核心算法，简化实现
 */
export class PinSnapManager {
  private graph: Graph;
  private readonly SNAP_DISTANCE = 30; // 吸附距离阈值，比原版更小，适合精确编辑
  // private readonly EDGE_TOLERANCE = 10; // 边缘容差

  constructor(graph: Graph) {
    this.graph = graph;
    this.setupEventListeners();
  }

  private setupEventListeners() {
    // 监听节点添加事件 - 当引脚被拖拽到画布时
    this.graph.on('node:added', ({ node }) => {
      if (node.shape === 'basic-pin') {
        // 延迟处理，确保节点完全添加到画布
        setTimeout(() => {
          this.handlePinDropped(node);
        }, 50);
      }
    });

    // 监听节点移动结束 - 执行最终吸附
    this.graph.on('node:moved', ({ node }) => {
      if (node.shape === 'basic-pin') {
        this.handlePinDropped(node);
      }
    });
  }

  /**
   * 处理引脚放置 - 核心吸附逻辑
   */
  private handlePinDropped(pinNode: Node) {
    const pinPos = pinNode.getPosition();
    console.log('🔧 处理引脚吸附，位置:', pinPos);
    
    // 获取所有非引脚节点
    const nodes = this.graph.getNodes().filter(n => 
      n.id !== pinNode.id && n.shape !== 'basic-pin'
    );
    
    let bestAttachment: {
      node: Node;
      snapPoint: Point;
      distance: number;
      edgeInfo: any;
    } | null = null;
    
    let minDistance = Infinity;
    
    // 遍历所有节点，寻找最佳吸附点
    for (const node of nodes) {
      const attachment = this.findBestSnapPoint(pinPos, node);
      if (attachment && attachment.distance < minDistance && attachment.distance < this.SNAP_DISTANCE) {
        minDistance = attachment.distance;
        bestAttachment = {
          node,
          snapPoint: attachment.snapPoint,
          distance: attachment.distance,
          edgeInfo: attachment.edgeInfo
        };
      }
    }
    
    if (bestAttachment) {
      console.log('✅ 找到最佳附着点:', bestAttachment);
      this.attachPinToNode(pinNode, bestAttachment);
    } else {
      console.log('ℹ️ 未找到合适的附着点，引脚保持当前位置');
      // 不删除引脚，让用户可以手动调整
    }
  }

  /**
   * 寻找最佳吸附点
   */
  private findBestSnapPoint(pinPos: Point, node: Node): {
    snapPoint: Point;
    distance: number;
    edgeInfo: any;
  } | null {
    const bbox = node.getBBox();
    const shape = node.shape;
    
    console.log(`🔍 分析节点 ${shape}:`, bbox);
    
    switch (shape) {
      case 'basic-triangle':
        return this.findTriangleSnapPoint(pinPos, bbox);
      case 'basic-circle':
        return this.findCircleSnapPoint(pinPos, bbox);
      default:
        return this.findRectSnapPoint(pinPos, bbox);
    }
  }

  /**
   * 三角形吸附点计算
   */
  private findTriangleSnapPoint(pinPos: Point, bbox: any): {
    snapPoint: Point;
    distance: number;
    edgeInfo: any;
  } | null {
    const { x, y, width, height } = bbox;
    
    // 三角形顶点：refPoints='50,0 100,100 0,100'
    const vertices: Point[] = [
      { x: x + width * 0.5, y: y },           // 顶点
      { x: x + width, y: y + height },        // 右下
      { x: x, y: y + height }                 // 左下
    ];
    
    // 三条边
    const edges: PolygonEdge[] = [
      {
        start: vertices[0], end: vertices[1],
        type: 'diagonal', 
        length: this.getDistance(vertices[0], vertices[1])
      },
      {
        start: vertices[1], end: vertices[2],
        type: 'horizontal',
        length: this.getDistance(vertices[1], vertices[2])
      },
      {
        start: vertices[2], end: vertices[0],
        type: 'diagonal',
        length: this.getDistance(vertices[2], vertices[0])
      }
    ];
    
    return this.findClosestEdgePoint(pinPos, edges);
  }

  /**
   * 圆形吸附点计算
   */
  private findCircleSnapPoint(pinPos: Point, bbox: any): {
    snapPoint: Point;
    distance: number;
    edgeInfo: any;
  } | null {
    const centerX = bbox.x + bbox.width / 2;
    const centerY = bbox.y + bbox.height / 2;
    const radius = bbox.width / 2;
    
    const angle = Math.atan2(pinPos.y - centerY, pinPos.x - centerX);
    const snapPoint = {
      x: centerX + radius * Math.cos(angle),
      y: centerY + radius * Math.sin(angle)
    };
    
    const distance = this.getDistance(pinPos, snapPoint);
    
    return {
      snapPoint,
      distance,
      edgeInfo: { type: 'circle', angle }
    };
  }

  /**
   * 矩形吸附点计算
   */
  private findRectSnapPoint(pinPos: Point, bbox: any): {
    snapPoint: Point;
    distance: number;
    edgeInfo: any;
  } | null {
    const { x, y, width, height } = bbox;
    
    const edges: PolygonEdge[] = [
      { start: { x, y }, end: { x: x + width, y }, type: 'horizontal', length: width },
      { start: { x: x + width, y }, end: { x: x + width, y: y + height }, type: 'vertical', length: height },
      { start: { x: x + width, y: y + height }, end: { x, y: y + height }, type: 'horizontal', length: width },
      { start: { x, y: y + height }, end: { x, y }, type: 'vertical', length: height }
    ];
    
    return this.findClosestEdgePoint(pinPos, edges);
  }

  /**
   * 寻找最近的边缘点
   */
  private findClosestEdgePoint(pinPos: Point, edges: PolygonEdge[]): {
    snapPoint: Point;
    distance: number;
    edgeInfo: any;
  } | null {
    let bestSnap = null;
    let minDistance = Infinity;
    
    edges.forEach((edge, index) => {
      const snapResult = this.getClosestPointOnLineSegment(pinPos, edge);
      if (snapResult.distance < minDistance) {
        minDistance = snapResult.distance;
        bestSnap = {
          snapPoint: snapResult.point,
          distance: snapResult.distance,
          edgeInfo: {
            edgeIndex: index,
            edgeType: edge.type,
            ratio: snapResult.ratio
          }
        };
      }
    });
    
    return bestSnap;
  }

  /**
   * 计算点到线段的最短距离和最近点
   */
  private getClosestPointOnLineSegment(point: Point, segment: LineSegment): {
    point: Point;
    distance: number;
    ratio: number;
  } {
    const { start, end } = segment;
    
    const segmentLength = this.getDistance(start, end);
    if (segmentLength === 0) {
      return {
        point: start,
        distance: this.getDistance(point, start),
        ratio: 0
      };
    }
    
    // 计算投影比例
    const dx = end.x - start.x;
    const dy = end.y - start.y;
    const t = Math.max(0, Math.min(1, 
      ((point.x - start.x) * dx + (point.y - start.y) * dy) / (dx * dx + dy * dy)
    ));
    
    // 计算最近点
    const closestPoint = {
      x: start.x + t * dx,
      y: start.y + t * dy
    };
    
    const distance = this.getDistance(point, closestPoint);
    
    return {
      point: closestPoint,
      distance,
      ratio: t
    };
  }

  /**
   * 计算两点距离
   */
  private getDistance(p1: Point, p2: Point): number {
    return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
  }



  /**
   * 附着引脚到节点
   */
  private attachPinToNode(pinNode: Node, attachment: any) {
    const { node: targetNode, snapPoint, edgeInfo } = attachment;
    
    console.log('📌 附着引脚到吸附点:', snapPoint);
    
    // 将引脚移动到吸附点，使其中心对齐
    const pinSize = pinNode.getSize();
    pinNode.position(snapPoint.x - pinSize.width / 2, snapPoint.y - pinSize.height / 2);
    
    // 可选：存储附着关系
    const attachedPins = targetNode.getData()?.attachedPins || [];
    attachedPins.push(pinNode.id);
    targetNode.setData({ ...targetNode.getData(), attachedPins });
    
    console.log('✅ 引脚成功附着到', targetNode.shape, '边缘类型:', edgeInfo.edgeType);
  }

  /**
   * 销毁管理器
   */
  destroy() {
    // 移除事件监听器
    this.graph.off('node:added');
    this.graph.off('node:moved');
  }
}