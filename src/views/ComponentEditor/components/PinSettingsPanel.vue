<template>
  <div class="floating-settings-panel" v-show="visible">
    <div class="panel-header">
      <span>引脚设置</span>
      <div class="header-actions">
        <div class="divider"></div>
        <el-icon class="arrow-icon" @click="closePanel">
          <ArrowRightBold />
        </el-icon>
      </div>
    </div>
    <div class="panel-content">
      <el-form :model="pinSettings" label-width="70px" class="pin-settings-form">
        <el-form-item label="引脚名">
          <el-input v-model="pinSettings.name" placeholder="请输入..." />
        </el-form-item>
        <el-form-item label="是否可见">
          <div class="switch-group">
            <span>否</span>
            <el-switch v-model="pinSettings.visible" />
            <span>是</span>
          </div>
        </el-form-item>
        <el-form-item label="数据类型">
          <el-checkbox v-model="pinSettings.dataType" true-label="real" false-label="" disabled>实数</el-checkbox>
        </el-form-item>
        <el-form-item label="引脚类型">
          <el-radio-group v-model="pinSettings.pinType">
            <el-radio label="electrical">电气连接</el-radio>
            <el-radio label="controlIn">控制输入</el-radio>
            <el-radio label="controlOut">控制输出</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="对应信号">
          <el-select v-model="pinSettings.signal" placeholder="请选择输入信号...">
            <el-option
              v-for="item in signalOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="引脚描述">
          <el-input v-model="pinSettings.description" type="textarea" :rows="2" placeholder="请输入..." />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, computed } from 'vue';
import { ElForm, ElFormItem, ElInput, ElSwitch, ElCheckbox, ElRadioGroup, ElRadio, ElSelect, ElOption, ElIcon } from 'element-plus';
import { ArrowRightBold } from '@element-plus/icons-vue';

interface IOItem {
  name: string;
  id: string;
}

export interface PinSettings {
  name: string;
  visible: boolean;
  dataType: string;
  pinType: 'electrical' | 'controlIn' | 'controlOut';
  signal: string;
  description: string;
}

const props = defineProps<{
  visible: boolean;
  settings: PinSettings;
  inputs: IOItem[];
  outputs: IOItem[];
}>();

const emit = defineEmits<{
  'update:visible': [value: boolean];
  'update:settings': [value: PinSettings];
}>();

const pinSettings = ref<PinSettings>(props.settings);

watch(() => props.settings, (newVal) => {
  pinSettings.value = newVal;
});

watch(() => pinSettings.value.pinType, () => {
  pinSettings.value.signal = '';
});

// 暂时在本地 使用name 因为目前么有id 等回显的时候  这个地方可能要特殊处理一下
const signalOptions = computed(() => {
  if (pinSettings.value.pinType !== 'controlOut') {
    return props.inputs.map(input => ({ label: input.name, value: input.id }));
  } else if (pinSettings.value.pinType === 'controlOut') {
    return props.outputs.map(output => ({ label: output.name, value: output.id }));
  } else {
    return [];
  }
});

const closePanel = () => {
  emit('update:visible', false);
};

watch(pinSettings, (newVal) => {
  emit('update:settings', newVal);
}, { deep: true });

</script>

<style scoped>

.floating-settings-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 300px;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.header-actions {
  display: flex;
  align-items: center;
}

.divider {
  width: 1px;
  height: 16px;
  background-color: #ccc;
  margin: 0 10px;
}

.arrow-icon {
  cursor: pointer;
}

.panel-content {
  padding: 15px;
}

.switch-group {
  display: flex;
  align-items: center;
}

.switch-group span {
  margin: 0 5px;
}
</style>