<template>
  <el-dialog
    v-model="dialogVisible"
    title="条件设置"
    
    :before-close="handleClose"
  >
    <div class="condition-settings-dialog">
      <el-form>
        <div class="section">
          <div class="section-header">
            <span>条件显示</span>
            <el-button type="primary" circle @click="addDisplayCondition"><el-icon><Plus /></el-icon></el-button>
          </div>
          <div v-for="(condition, index) in displayConditions" :key="index" class="condition-row">
            <span>{{ index + 1 }}. 当</span>
            <el-form-item>
              <el-select v-model="condition.param" placeholder="请选择参数" style="width: 150px;">
                <el-option v-for="param in parameters" :key="param.label" :label="param.label" :value="param.label"></el-option>
              </el-select>
            </el-form-item>
            <span>为</span>
            <el-form-item>
              <div class="constraint-inputs">
                <el-input-number v-model="condition.value_min" :controls="false" placeholder="区间左值" style="width: 90px;" />
                <span class="separator">-</span>
                <el-input-number v-model="condition.value_max" :controls="false" placeholder="区间右值" style="width: 90px;" />
              </div>
            </el-form-item>
            <span>时, 则</span>
            <el-form-item>
              <el-select v-model="condition.action" placeholder="请选择操作" style="width: 100px;">
                <el-option label="显示" value="1"></el-option>
                <el-option label="不显示" value="0"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-select v-model="condition.target" placeholder="请选择参数" style="width: 150px;">
                <el-option v-for="param in parameters" :key="param.label" :label="param.label" :value="param.label"></el-option>
              </el-select>
            </el-form-item>
            <el-button type="danger" circle @click="removeDisplayCondition(index)" v-if="index > 0"><el-icon><Close /></el-icon></el-button>
          </div>
        </div>
        <div class="section">
          <div class="section-header">
            <span>条件使用</span>
            <el-button type="primary" circle @click="addUsageCondition"><el-icon><Plus /></el-icon></el-button>
          </div>
          <div v-for="(condition, index) in usageConditions" :key="index" class="condition-row">
            <span>{{ index + 1 }}. 当</span>
            <el-form-item>
              <el-select v-model="condition.param" placeholder="请选择参数" style="width: 150px;">
                <el-option v-for="param in parameters" :key="param.label" :label="param.label" :value="param.label"></el-option>
              </el-select>
            </el-form-item>
            <span>为</span>
            <el-form-item>
              <el-input-number v-model="condition.value" :controls="false" placeholder="请输入值" style="width: 90px;" />
            </el-form-item>
            <span>时, 则</span>
            <el-form-item>
              <el-select v-model="condition.result" placeholder="请选择约束结果" style="width: 150px;">
                <el-option label="导通" value="on"></el-option>
                <el-option label="截止" value="off"></el-option>
              </el-select>
            </el-form-item>
            <el-button type="danger" circle @click="removeUsageCondition(index)" v-if="index > 0"><el-icon><Close /></el-icon></el-button>
          </div>
        </div>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, watch } from 'vue';
import { cloneDeep } from 'lodash-es';
import { ElDialog, ElButton, ElSelect, ElInput, ElIcon, ElForm, ElFormItem, ElInputNumber } from 'element-plus';
import { Plus, Close } from '@element-plus/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  parameters: {
    type: Array as () => Array<{ label: string; unit: string }>,
    default: () => []
  },
  conditions: {
    type: Object as () => ({ displayConditions: any[], usageConditions: any[] }),
    default: () => ({ displayConditions: [], usageConditions: [] })
  }
});

const emit = defineEmits(['update:visible', 'confirm']);

const dialogVisible = ref(props.visible);

watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    if (props.conditions && props.conditions.displayConditions && props.conditions.displayConditions.length > 0) {
      displayConditions.value = cloneDeep(props.conditions.displayConditions);
    } else {
      displayConditions.value = [{ param: '', value_min: undefined, value_max: undefined, action: '', target: '' }];
    }
    if (props.conditions && props.conditions.usageConditions && props.conditions.usageConditions.length > 0) {
      usageConditions.value = cloneDeep(props.conditions.usageConditions);
    } else {
      usageConditions.value = [{ param: '', value: undefined, result: '' }];
    }
  }
});

const handleClose = () => {
  dialogVisible.value = false;
  emit('update:visible', false);
};

const displayConditions = ref([
  { param: '', value_min: undefined, value_max: undefined, action: '', target: '' }
]);

const usageConditions = ref([
  { param: '', value: undefined, result: '' }
]);

const addDisplayCondition = () => {
  displayConditions.value.push({ param: '', value_min: undefined, value_max: undefined, action: '', target: '' });
};

const removeDisplayCondition = (index: number) => {
  displayConditions.value.splice(index, 1);
};

const addUsageCondition = () => {
  usageConditions.value.push({ param: '', value: undefined, result: '' });
};

const removeUsageCondition = (index: number) => {
  usageConditions.value.splice(index, 1);
};

const handleConfirm = () => {
  emit('confirm', { displayConditions: displayConditions.value, usageConditions: usageConditions.value });
  emit('update:visible', false);
};
</script>

<style scoped>
.condition-settings-dialog {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section {
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header span {
  font-size: 16px;
  font-weight: bold;
}

.condition-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.condition-row span {
  white-space: nowrap;
}

.condition-row .el-form-item {
  margin-bottom: 0;
}

.condition-row .el-button {
  margin-left: 10px;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}
</style>