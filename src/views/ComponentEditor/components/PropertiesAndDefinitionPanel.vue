<template>
  <div class="properties-panel">
    <div class="panel-header">
      <el-tabs v-model="activeTab" class="panel-tabs">
        <el-tab-pane label="属性" name="properties"></el-tab-pane>
        <el-tab-pane label="定义" name="definition"></el-tab-pane>
      </el-tabs>
    </div>
    <div class="properties-content">
      <!-- 属性面板 -->
      <div v-if="activeTab === 'properties'">
        <div v-if="selectedNode" class="property-form">
          <div class="property-group">
            <label>X 坐标</label>
            <el-input-number
              :model-value="nodeX"
              @update:model-value="(val) => updateNodePosition({ x: val, y: nodeY })"
              :precision="0"
              :step="1"
            />
          </div>
          <div class="property-group">
            <label>Y 坐标</label>
            <el-input-number
              :model-value="nodeY"
              @update:model-value="(val) => updateNodePosition({ x: nodeX, y: val })"
              :precision="0"
              :step="1"
            />
          </div>
          
          <div class="property-group">
            <label>宽度</label>
            <el-input-number
              :model-value="nodeWidth"
              @update:model-value="(val) => updateNodeSize({ width: val, height: nodeHeight })"
              :min="1"
              :max="500"
              :step="1"
            />
          </div>
          <div class="property-group">
            <label>高度</label>
            <el-input-number
              :model-value="nodeHeight"
              @update:model-value="(val) => updateNodeSize({ width: nodeWidth, height: val })"
              :min="1"
              :max="500"
              :step="1"
            />
          </div>
          <div
            class="property-group"
            v-if="selectedNodeProps.shape === 'basic-text'"
          >
            <label>文本</label>
            <el-input
              :model-value="nodeText"
              @update:model-value="updateNodeText"
              placeholder="输入文本"
            />
          </div>
          <div
            class="property-group"
            v-if="selectedNodeProps.shape === 'basic-text'"
          >
            <label>文本颜色</label>
            <el-color-picker :model-value="nodeColor" @update:model-value="updateNodeColor" />
          </div>
          <div
            class="property-group"
            v-if="selectedNodeProps.shape !== 'basic-pin' && selectedNodeProps.shape !== 'basic-text' && selectedNodeProps.name !== 'line'"
          >
            <label>线条宽度</label>
            <el-input-number
              :model-value="nodeStrokeWidth"
              @update:model-value="(val) => updateNodeStroke({ strokeWidth: val, strokeColor: nodeStrokeColor })"
              :min="1"
              :max="10"
              :step="1"
            />
          </div>
          <div
            class="property-group"
            v-if="selectedNodeProps.shape !== 'basic-pin' && selectedNodeProps.shape !== 'basic-text' && selectedNodeProps.name !== 'line'"
          >
            <label>线条颜色</label>
            <el-color-picker
              :model-value="nodeStrokeColor"
              @update:model-value="(val) => updateNodeStroke({ strokeWidth: nodeStrokeWidth, strokeColor: val })"
            />
          </div>
          <div
            class="property-group"
            v-if="selectedNodeProps.shape !== 'basic-pin' && selectedNodeProps.shape !== 'basic-text' && selectedNodeProps.name !== 'line'"
          >
            <label>填充颜色</label>
            <el-color-picker
              :model-value="nodeFillColor"
              show-alpha
              @update:model-value="updateNodeFill"
            />
          </div>
        </div>
        <div v-else class="no-selection">
          <p>请选择一个元件</p>
        </div>
      </div>
      
      <!-- 定义面板 -->
      <div v-if="activeTab === 'definition'" class="definition-form">
        <div class="definition-basic-info">
          <div class="form-row">
            <label>元件名称</label>
            <el-input
              v-model="localComponentDefinition.name"
              readonly
            />
          </div>
          
          <div class="form-row">
            <label>元件关键字</label>
            <el-input
              v-model="localComponentDefinition.keyword"
              readonly
            />
          </div>
          
          <div class="form-row">
            <label>元件备注</label>
            <el-input
              v-model="localComponentDefinition.description"
              readonly
              type="textarea"
              :rows="3"
              resize="none"
            />
          </div>
        </div>
        
        <div class="scrollable-list">
          <div class="definition-params-section">
            <div class="section-header">
              <span>参数</span>
              <div class="header-actions">
                <el-button link type="primary" @click="showConditionSettings">条件</el-button>
                <el-button size="small" type="primary" @click="addParameter">
                  <el-icon><Plus /></el-icon>
                </el-button>
              </div>
            </div>
            
            <div v-for="(param, index) in localComponentDefinition.parameters" :key="index" class="parameter-item">
              <div class="param-header">
                <span class="param-title">参数{{ index + 1 }}</span>
                <div class="param-actions">
                  <el-button size="small" type="danger" @click="removeParameter(index)">
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
              </div> 
              
              <div class="param-content">
                <div class="form-row">
                  <label>参数标签名</label>
                  <el-input v-model="param.label" placeholder="请输入" />
                </div>
                <div class="form-row">
                  <label>单位</label>
                  <el-input v-model="param.unit" placeholder="请输入" />
                </div>
                <div class="form-row">
                  <label>参数约束</label>
                  <div class="constraint-inputs">
                    <el-input-number v-model="param.constraint_min" :controls="false" placeholder="最小值" />
                    <span class="separator">-</span>
                    <el-input-number v-model="param.constraint_max" :controls="false" placeholder="最大值" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="definition-io-section">
            <div class="section-header">
              <span>输入/输出配置</span>
            </div>
            
            <div class="io-subsection">
              <div class="subsection-header">
                <span>输入配置</span>
                <el-button size="small" type="primary" @click="addInput">
                  <el-icon><Plus /></el-icon>
                </el-button>
              </div>
              
              <div v-for="(input, index) in localComponentDefinition.inputs" :key="input.id" class="io-item">
                <div class="io-header">
                  <div class="io-content">
                    <div class="form-row">
                      <el-input v-model="input.name" placeholder="请输入"/>
                    </div>
                  </div>
                  <div class="io-actions">
                    <el-button size="small" type="danger" @click="removeInput(index)">
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="io-subsection">
              <div class="subsection-header">
                <span>输出配置</span>
                <el-button size="small" type="primary" @click="addOutput">
                  <el-icon><Plus /></el-icon>
                </el-button>
              </div>
              
              <div v-for="(output, index) in localComponentDefinition.outputs" :key="output.id" class="io-item">
                <div class="io-header">
                  <div class="io-content">
                    <div class="form-row">
                      <el-input v-model="output.name" placeholder="请输入" />
                    </div>
                  </div>
                  <div class="io-actions">
                    <el-button size="small" type="danger" @click="removeOutput(index)">
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElInputNumber, ElInput, ElColorPicker, ElTabs, ElTabPane, ElButton, ElIcon } from 'element-plus';
import { Plus, Close } from '@element-plus/icons-vue';

// 定义 props
const props = defineProps({
  selectedNode: {
    type: Object,
    default: null,
  },
  selectedNodeProps: {
    type: Object,
    default: null,
  },
  componentDefinition: {
    type: Object,
    required: true,
  },
});

// 定义 emits
const emit = defineEmits([
  'update:componentDefinition',
  'show-condition-settings',
  'update:node-position',
  'update:node-size',
  'update:node-text',
  'update:node-color',
  'update:node-stroke',
  'update:node-fill',
]);

const localComponentDefinition = computed({
  get: () => props.componentDefinition,
  set: (value) => emit('update:componentDefinition', value),
});

const activeTab = ref('properties');

// 节点属性的计算属性
const nodeX = computed(() => props.selectedNodeProps?.x || 0);
const nodeY = computed(() => props.selectedNodeProps?.y || 0);
const nodeWidth = computed(() => props.selectedNodeProps?.width || 0);
const nodeHeight = computed(() => props.selectedNodeProps?.height || 0);
const nodeText = computed(() => props.selectedNodeProps?.text || '');
const nodeColor = computed(() => props.selectedNodeProps?.color || '#000000');
const nodeStrokeWidth = computed(() => props.selectedNodeProps?.strokeWidth || 1);
const nodeStrokeColor = computed(() => props.selectedNodeProps?.strokeColor || '#000000');
const nodeFillColor = computed(() => props.selectedNodeProps?.fillColor || '#FFFFFF');

// 定义面板方法
const addParameter = () => {
  const newParams = [...localComponentDefinition.value.parameters, { required: false, label: '', unit: '', constraint_min: null, constraint_max: null }];
  localComponentDefinition.value = { ...localComponentDefinition.value, parameters: newParams };
};

const removeParameter = (index: number) => {
  const newParams = [...localComponentDefinition.value.parameters];
  newParams.splice(index, 1);
  localComponentDefinition.value = { ...localComponentDefinition.value, parameters: newParams };
};

const addInput = () => {
  const newInputs = [...localComponentDefinition.value.inputs, { id: Date.now().toString(), name: '' }];
  localComponentDefinition.value = { ...localComponentDefinition.value, inputs: newInputs };
};

const removeInput = (index: number) => {
  const newInputs = localComponentDefinition.value.inputs.filter((_, i) => i !== index);
  localComponentDefinition.value = { ...localComponentDefinition.value, inputs: newInputs };
};

const addOutput = () => {
  const newOutputs = [...localComponentDefinition.value.outputs, { id: Date.now().toString(), name: '' }];
  localComponentDefinition.value = { ...localComponentDefinition.value, outputs: newOutputs };
};

const removeOutput = (index: number) => {
  const newOutputs = localComponentDefinition.value.outputs.filter((_, i) => i !== index);
  localComponentDefinition.value = { ...localComponentDefinition.value, outputs: newOutputs };
};

const showConditionSettings = () => {
  emit('show-condition-settings');
};

// 代理事件到父组件
const updateNodePosition = (position) => {
  emit('update:node-position', position);
};
const updateNodeSize = (size) => {
  emit('update:node-size', size);
};
const updateNodeText = (value: string) => emit('update:node-text', value);
const updateNodeColor = (value: string) => emit('update:node-color', value);
const updateNodeStroke = (stroke) => {
  emit('update:node-stroke', stroke);
};
const updateNodeFill = (value: string) => emit('update:node-fill', value);

</script>

<style scoped>
.properties-panel {
  width: 320px;
  background-color: #ffffff;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.05);
}

.panel-header {
  padding: 0;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f7f9fa;
}

.panel-tabs {
  width: 100%;
}

:deep(.panel-tabs .el-tabs__nav) {
  display: flex;
  width: 100%;
}

:deep(.panel-tabs .el-tabs__item) {
  flex: 1;
  text-align: center;
  padding: 0;
}

:deep(.el-tabs__header) {
  margin: 0;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
}

:deep(.el-tabs__active-bar) {
  background-color: #409eff;
  height: 2px;
}

.properties-content {
  flex-grow: 1;
  overflow-y: auto;
  padding: 16px;
}

.property-form, .definition-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.property-group, .form-row {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.property-group label, .form-row label {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-color-picker) {
  width: 100%;
}

:deep(.el-color-picker__trigger) {
  width: 100%;
  height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.no-selection {
  text-align: center;
  margin-top: 40px;
  color: #888;
}

.definition-basic-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e8e8e8;
}

.scrollable-list {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto;
  padding-right: 4px;
  min-height: 0;
}

.scrollable-list::-webkit-scrollbar {
  width: 6px;
}

.scrollable-list::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 3px;
}

.scrollable-list::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}

.definition-params-section, .definition-io-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.parameter-item {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
  background-color: #fdfdfd;
  transition: all 0.3s ease;
}

.parameter-item:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.param-title {
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.param-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.constraint-inputs {
  display: flex;
  align-items: center;
  gap: 12px;
}

.constraint-inputs .separator {
  color: #909399;
  margin: 0;
}

.io-subsection {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.subsection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-top: 12px;
}

.io-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.io-item:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.io-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.io-content {
  flex-grow: 1;
}

.io-actions {
  flex-shrink: 0;
}

:deep(.el-button--small) {
  padding: 6px 12px;
  font-size: 12px;
}

:deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
}

:deep(.el-textarea__inner) {
  min-height: 80px;
}
</style>