<template>
  <div class="properties-panel">
    <div class="panel-header">
      <el-tabs v-model="activeTab" class="panel-tabs">
        <el-tab-pane label="属性" name="properties"></el-tab-pane>
        <el-tab-pane label="定义" name="definition"></el-tab-pane>
      </el-tabs>
    </div>
    <div class="properties-content">
      <!-- 属性面板 -->
      <div v-if="activeTab === 'properties'">
        <div v-if="selectedNode" class="property-form">
          <div class="property-group">
            <label>X 坐标</label>
            <el-input-number
              v-model="nodeX"
              :precision="0"
              :step="1"
              @change="updateNodePosition"
            />
          </div>
          <div class="property-group">
            <label>Y 坐标</label>
            <el-input-number
              v-model="nodeY"
              :precision="0"
              :step="1"
              @change="updateNodePosition"
            />
          </div>
          
          <div class="property-group">
            <label>宽度</label>
            <el-input-number
              v-model="nodeWidth"
              :min="1"
              :max="500"
              :step="1"
              @change="updateNodeSize"
            />
          </div>
          <div class="property-group">
            <label>高度</label>
            <el-input-number
              v-model="nodeHeight"
              :min="1"
              :max="500"
              :step="1"
              @change="updateNodeSize"
            />
          </div>
          <div
            class="property-group"
            v-if="selectedNode.shape === 'basic-text'"
          >
            <label>文本</label>
            <el-input
              v-model="nodeText"
              @input="updateNodeText"
              placeholder="输入文本"
            />
          </div>
          <div class="property-group">
            <label>颜色</label>
            <el-color-picker v-model="nodeColor" @change="updateNodeColor" />
          </div>
          <div
            class="property-group"
            v-if="selectedNode.shape !== 'basic-pin' && selectedNode.name !== 'line'"
          >
            <label>线条宽度</label>
            <el-input-number
              v-model="nodeStrokeWidth"
              :min="1"
              :max="10"
              :step="1"
              @change="updateNodeStroke"
            />
          </div>
          <div
            class="property-group"
            v-if="selectedNode.shape !== 'basic-pin' && selectedNode.name !== 'line'"
          >
            <label>线条颜色</label>
            <el-color-picker
              v-model="nodeStrokeColor"
              @change="updateNodeStroke"
            />
          </div>
          <div
            class="property-group"
            v-if="selectedNode.shape !== 'basic-pin' && selectedNode.name !== 'line'"
          >
            <label>填充颜色</label>
            <el-color-picker
              v-model="nodeFillColor"
              show-alpha
              @change="updateNodeFill"
            />
          </div>
        </div>
        <div v-else class="no-selection">
          <p>请选择一个元件</p>
        </div>
      </div>
      
      <!-- 定义面板 -->
      <div v-if="activeTab === 'definition'" class="definition-form">
        <div class="definition-basic-info">
          <div class="form-row">
            <label>元件名称</label>
            <el-input
              v-model="localComponentDefinition.name"
              readonly
            />
          </div>
          
          <div class="form-row">
            <label>元件关键字</label>
            <el-input
              v-model="localComponentDefinition.keyword"
              readonly
            />
          </div>
          
          <div class="form-row">
            <label>元件备注</label>
            <el-input
              v-model="localComponentDefinition.description"
              readonly
              type="textarea"
              :rows="3"
              resize="none"
            />
          </div>
        </div>
        
        <div class="scrollable-list">
          <div class="definition-params-section">
            <div class="section-header">
              <span>参数</span>
              <div class="header-actions">
                <el-button link type="primary" @click="$emit('open-condition-settings')">条件</el-button>
                <el-button size="small" type="primary" @click="addParameter">
                  <el-icon><Plus /></el-icon>
                </el-button>
              </div>
            </div>
            
            <div v-for="(param, index) in localComponentDefinition.parameters" :key="index" class="parameter-item">
              <div class="param-header">
                <span class="param-title">参数{{ index + 1 }}</span>
                <div class="param-actions">
                  <el-button size="small" type="danger" @click="removeParameter(index)">
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
              </div> 
              
              <div class="param-content">
                <div class="form-row">
                  <label>参数标签名</label>
                  <el-input v-model="param.label" placeholder="请输入" />
                </div>
                <div class="form-row">
                  <label>单位</label>
                  <el-input v-model="param.unit" placeholder="请输入" />
                </div>
                <div class="form-row">
                  <label>参数约束</label>
                  <div class="constraint-inputs">
                    <el-input-number v-model="param.constraint_min" :controls="false" placeholder="最小值" />
                    <span class="separator">-</span>
                    <el-input-number v-model="param.constraint_max" :controls="false" placeholder="最大值" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="definition-io-section">
            <div class="section-header">
              <span>输入/输出配置</span>
            </div>
            
            <div class="io-subsection">
              <div class="subsection-header">
                <span>输入配置</span>
                <el-button size="small" type="primary" @click="addInput">
                  <el-icon><Plus /></el-icon>
                </el-button>
              </div>
              
              <div v-for="(input, index) in localComponentDefinition.inputs" :key="index" class="io-item">
                <div class="io-header">
                  <div class="io-content">
                    <div class="form-row">
                      <el-input v-model="input.name" placeholder="请输入"/>
                    </div>
                  </div>
                  <div class="io-actions">
                    <el-button size="small" type="danger" @click="removeInput(index)">
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="io-subsection">
              <div class="subsection-header">
                <span>输出配置</span>
                <el-button size="small" type="primary" @click="addOutput">
                  <el-icon><Plus /></el-icon>
                </el-button>
              </div>
              
              <div v-for="(output, index) in localComponentDefinition.outputs" :key="index" class="io-item">
                <div class="io-header">
                  <div class="io-content">
                    <div class="form-row">
                      <el-input v-model="output.name" placeholder="请输入" />
                    </div>
                  </div>
                  <div class="io-actions">
                    <el-button size="small" type="danger" @click="removeOutput(index)">
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive } from 'vue';
import { ElInput, ElInputNumber, ElColorPicker, ElTabs, ElTabPane, ElButton, ElIcon } from 'element-plus';
import { Plus, Close } from '@element-plus/icons-vue';

const props = defineProps({
  selectedNode: { type: Object, default: null },
  componentDefinition: { type: Object, required: true },
});

const emit = defineEmits([
  'update:componentDefinition',
  'open-condition-settings',
  'update-node',
]);

const activeTab = ref('properties');

// Local state for node properties
const nodeX = ref(0);
const nodeY = ref(0);
const nodeWidth = ref(0);
const nodeHeight = ref(0);
const nodeText = ref('');
const nodeColor = ref('#000000');
const nodeStrokeWidth = ref(2);
const nodeStrokeColor = ref('#000000');
const nodeFillColor = ref('rgba(255,255,255,0)');

// Watch for changes in the selected node from the parent
watch(() => props.selectedNode, (newNode) => {
  if (newNode) {
    const pos = newNode.getPosition();
    const size = newNode.getSize();
    nodeX.value = pos.x;
    nodeY.value = pos.y;
    nodeWidth.value = size.width;
    nodeHeight.value = size.height;

    if (newNode.shape === 'basic-text') {
      nodeText.value = newNode.attr('text/text') || '';
    } 
    nodeColor.value = newNode.attr('body/stroke') || '#000000';
    nodeStrokeWidth.value = newNode.attr('body/strokeWidth') || 2;
    nodeStrokeColor.value = newNode.attr('body/stroke') || '#000000';
    nodeFillColor.value = newNode.attr('body/fill') || 'rgba(255,255,255,0)';

  } else {
    nodeX.value = 0;
    nodeY.value = 0;
    nodeWidth.value = 0;
    nodeHeight.value = 0;
    nodeText.value = "";
    nodeColor.value = "#000000";
    nodeStrokeWidth.value = 2;
    nodeStrokeColor.value = "#000000";
    nodeFillColor.value = "rgba(255,255,255,0)";
  }
}, { immediate: true, deep: true });

// Methods to emit updates to the parent
const updateNodePosition = () => {
  emit('update-node', { key: 'position', value: { x: nodeX.value, y: nodeY.value } });
};

const updateNodeSize = () => {
  emit('update-node', { key: 'size', value: { width: nodeWidth.value, height: nodeHeight.value } });
};

const updateNodeText = (newText: string) => {
  emit('update-node', { key: 'text', value: newText });
};

const updateNodeColor = (color: string) => {
  emit('update-node', { key: 'color', value: color });
};

const updateNodeStroke = () => {
  emit('update-node', { key: 'stroke', value: { strokeWidth: nodeStrokeWidth.value, stroke: nodeStrokeColor.value } });
};

const updateNodeFill = (color: string) => {
  emit('update-node', { key: 'fill', value: color });
};

// Local reactive copy for componentDefinition
const localComponentDefinition = reactive(JSON.parse(JSON.stringify(props.componentDefinition)));

watch(() => props.componentDefinition, (newVal) => {
  Object.assign(localComponentDefinition, JSON.parse(JSON.stringify(newVal)));
}, { deep: true });

watch(localComponentDefinition, (newVal) => {
  emit('update:componentDefinition', JSON.parse(JSON.stringify(newVal)));
}, { deep: true });

const addParameter = () => {
  localComponentDefinition.parameters.push({ required: false, label: '', unit: '', constraint_min: undefined, constraint_max: undefined });
};

const removeParameter = (index: number) => {
  localComponentDefinition.parameters.splice(index, 1);
};

const addInput = () => {
  localComponentDefinition.inputs.push({ name: '' });
};

const removeInput = (index: number) => {
  localComponentDefinition.inputs.splice(index, 1);
};

const addOutput = () => {
  localComponentDefinition.outputs.push({ name: '' });
};

const removeOutput = (index: number) => {
  localComponentDefinition.outputs.splice(index, 1);
};

</script>

<style scoped>
.properties-panel {
  width: 320px;
  background-color: #ffffff;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.panel-header {
  padding: 0 16px;
  border-bottom: 1px solid #e0e0e0;
}

.panel-tabs {
  height: 48px;
}

.properties-content {
  flex-grow: 1;
  overflow-y: auto;
}

.property-form, .definition-form {
  padding: 16px;
}

.no-selection {
  text-align: center;
  margin-top: 40px;
  color: #999;
}

.property-group, .form-row {
  margin-bottom: 16px;
}

.property-group label, .form-row label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
}

.definition-basic-info {
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.scrollable-list {
  max-height: calc(100vh - 320px); /* Adjust based on your layout */
  overflow-y: auto;
}

.definition-params-section, .definition-io-section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 500;
}

.parameter-item, .io-item {
  background-color: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
}

.param-header, .io-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.param-title {
  font-weight: 500;
}

.constraint-inputs {
  display: flex;
  align-items: center;
}

.constraint-inputs .separator {
  margin: 0 8px;
}

.io-subsection {
  margin-bottom: 16px;
}

.subsection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
}
</style>