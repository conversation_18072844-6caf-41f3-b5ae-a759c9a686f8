import { Graph } from '@antv/x6';

// 统一的元件样式配置
const UNIFIED_STYLE = {
  stroke: '#000000',     // 统一黑色边框
  strokeWidth: 2,        // 默认线条宽度2px
  fill: 'transparent',   // 透明填充
  pinColor: '#1976d2',   // 引脚蓝色
  pinSize: 4,            // 引脚固定4px
};

// 1. 注册基础矩形元件
export const registerBasicRect = () => {
  Graph.registerNode(
    'basic-rect',
    {
      inherit: 'rect',
      width: 120, // 已经是5的倍数
      height: 80,  // 已经是5的倍数
      attrs: {
        body: {
          strokeWidth: UNIFIED_STYLE.strokeWidth,
          stroke: UNIFIED_STYLE.stroke,
          fill: UNIFIED_STYLE.fill,
          rx: 5, // 调整为5的倍数
          ry: 5,
        },
      },
    },
    true
  );
};

// 2. 注册基础圆形元件
export const registerBasicCircle = () => {
  Graph.registerNode(
    'basic-circle',
    {
      inherit: 'circle',
      width: 80,  // 已经是5的倍数
      height: 80, // 已经是5的倍数
      attrs: {
        body: {
          strokeWidth: UNIFIED_STYLE.strokeWidth,
          stroke: UNIFIED_STYLE.stroke,
          fill: UNIFIED_STYLE.fill,
        },
      },
    },
    true
  );
};

// 3. 注册基础三角形元件
export const registerBasicTriangle = () => {
  Graph.registerNode(
    'basic-triangle',
    {
      inherit: 'polygon',
      width: 80,  // 已经是5的倍数
      height: 80, // 已经是5的倍数
      attrs: {
        body: {
          strokeWidth: UNIFIED_STYLE.strokeWidth,
          stroke: UNIFIED_STYLE.stroke,
          fill: UNIFIED_STYLE.fill,
          refPoints: '50,0 100,100 0,100', // 三角形顶点
        },
      },
    },
    true
  );
};

// 4. 注册基础线条元件
export const registerBasicLine = () => {
  Graph.registerNode(
    'basic-line',
    {
      inherit: 'rect', // 继承 rect，利用其天生的缩放能力
      width: 100,      // 默认长度
      height: UNIFIED_STYLE.strokeWidth, // 高度就是线条的粗细
      attrs: {
        body: {
          fill: UNIFIED_STYLE.stroke, // 使用黑色填充来模拟线条
          stroke: 'none', // 不需要边框
        },
      },
    
    },
    true
  );
};

// 5. 注册基础文本元件
export const registerBasicText = () => {
  Graph.registerNode(
    'basic-text',
    {
      inherit: 'rect',
      width: 80,
      height: 30,
      attrs: {
        body: {
          fill: 'transparent',
          stroke: 'transparent',
        },
        text: {
          fontSize: 16,
          fill: UNIFIED_STYLE.stroke, // 使用黑色文本
          text: '文本',
          textAnchor: 'middle',
          textVerticalAnchor: 'middle',
          fontWeight: 'normal',
        },
      },
    },
    true
  );
};

// 6. 注册基础引脚元件 - 智能显示虚线边框
export const registerBasicPin = () => {
  Graph.registerNode(
    'basic-pin',
    {
      inherit: 'rect',
      width: 25,  // 增大选区宽度到25px，更容易选中
      height: 25, // 增大选区高度到25px，更容易选中
      attrs: {
        // 虚线矩形边框 - 默认隐藏，但选区更大
        body: {
          strokeWidth: 1,
          stroke: UNIFIED_STYLE.pinColor, // 蓝色虚线
          strokeDasharray: '2,2', // 虚线样式
          fill: 'transparent', // 透明填充
          rx: 3, // 圆角稍微增大
          ry: 3,
          opacity: 0, // 默认隐藏边框
          cursor: 'pointer', // 默认指针光标
          transition: 'opacity 0.2s ease', // 平滑过渡效果
        },
        // 中心蓝点 - 始终显示，大小不变
        dot: {
          r: 2, // 半径2px的小圆点，保持原有大小
          fill: UNIFIED_STYLE.pinColor,
          stroke: 'none',
          cx: 12.5, // 居中位置 (25/2 = 12.5)
          cy: 12.5,
        },
      },
      markup: [
        {
          tagName: 'rect',
          selector: 'body',
        },
        {
          tagName: 'circle',
          selector: 'dot',
        },
      ],
    },
    true
  );
};

// 注册所有基本元件
export const registerAllBasicElements = () => {
  registerBasicRect();
  registerBasicCircle();
  registerBasicTriangle();
  registerBasicLine();
  registerBasicText();
  registerBasicPin();
  
  console.log('✅ 所有基本元件注册完成（统一黑色样式） - v4');
};

// 导出基本元件配置列表
export const basicElementsList = [
  {
    key: 'basic-rect',
    name: '矩形',
    category: '基础图形',
    icon: '□',
    description: '基础矩形元件',
  },
  {
    key: 'basic-circle', 
    name: '圆形',
    category: '基础图形',
    icon: '○',
    description: '基础圆形元件',
  },
  {
    key: 'basic-triangle',
    name: '三角形',
    category: '基础图形', 
    icon: '△',
    description: '基础三角形元件',
  },
  {
    key: 'basic-line',
    name: '线条',
    category: '基础图形',
    icon: '—',
    description: '基础线条元件',
  },
  {
    key: 'basic-text',
    name: '文本',
    category: '基础图形',
    icon: 'T',
    description: '基础文本元件',
  },
  {
    key: 'basic-pin',
    name: '引脚',
    category: '连接元件',
    icon: '●',
    description: '基础引脚元件',
  },
];
