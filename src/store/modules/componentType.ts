import { defineStore } from 'pinia';
import { getComponentTypeList } from '@/api/index';

export const useComponentTypeStore = defineStore('componentType', {
  state: () => ({
    types: [] as any[],
    loading: false,
    error: null as any,
  }),
  actions: {
    async fetchComponentTypes() {
      this.loading = true;
      this.error = null;
      try {
        const res = await getComponentTypeList();
        if (res.data && Array.isArray(res.data)) {
          this.types = res.data;
        }
      } catch (error) {
        this.error = error;
      } finally {
        this.loading = false;
      }
    },
  },
});