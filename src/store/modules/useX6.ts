import { defineStore } from "pinia";
import { ref } from "vue";

// 引脚接口定义
export interface ComponentPin {
  id: string;
  type: 'input' | 'output';
  position: {
    x: number; // 绝对位置坐标
    y: number; // 绝对位置坐标
  };
  label?: string;
  showLabel?: boolean;
  style?: any;
  attachedToNodeId?: string;
  originalCell?: any; // 保存原始cell信息
}

// 组合组件信息接口
export interface ComponentInfo {
  name: string;
  pins: ComponentPin[]; // 外部引脚
  nodes?: any[]; // 内部节点
  edges?: any[]; // 内部连线
  width?: number;
  height?: number;
  style?: {
    backgroundColor?: string;
    borderColor?: string;
    textColor?: string;
  };
  originalJsonData?: any; // 保存原始图数据
}

// 用户自定义组件接口
export interface UserComponent {
  id: string;
  name: string;
  svgData: string; // SVG数据（保留兼容性）
  jsonData: any; // 组件的JSON数据（旧格式）
  componentInfo?: ComponentInfo; // 新的组件信息格式
  renderType?: 'svg' | 'json'; // 渲染类型
  createdAt: number;
}

export const useX6Store = defineStore("x6", () => {
  const initJSON = ref<any>(null); // 保存画布
  const currentNode = ref<any>(null); // 当前选中节点
  const userComponents = ref<UserComponent[]>([]); // 用户自定义组件列表

  function currentNodeChange(item: any) {
    currentNode.value = item;
  }
  
  function initJSONChange(item: any) {
    initJSON.value = item;
  }

  // 保存用户自定义组件（SVG方式，保留兼容性）
  function saveUserComponent(name: string, jsonData: any, svgData: string, uniqueId?: string) {
    console.log('---------- ',svgData)
  
    const component: UserComponent = {
      id: uniqueId || `user_component_${Date.now()}`,
      name: name || `自定义组件${userComponents.value.length + 1}`,
      svgData: svgData,
      jsonData: jsonData,
      renderType: 'svg',
      createdAt: Date.now()
    };
    
    userComponents.value.push(component);
    
    // 保存到localStorage
    try {
      localStorage.setItem('userComponents', JSON.stringify(userComponents.value));
      // 验证是否保存成功
      const saved = localStorage.getItem('userComponents');
      if (!saved) {
        throw new Error('保存失败，localStorage可能已满');
      }
    } catch (error) {
      console.error('保存用户组件到localStorage失败:', error);
      // 如果保存失败，从数组中移除该组件
      const index = userComponents.value.findIndex(comp => comp.id === component.id);
      if (index > -1) {
        userComponents.value.splice(index, 1);
      }
      return null;
    }
    
    return component;
  }

  // 新增：保存用户自定义组件（JSON方式）
  function saveUserComponentAsJson(name: string, jsonData: any, componentInfo: ComponentInfo, uniqueId?: string) {
    console.log('保存JSON组件:', { name, componentInfo });
  
    const component: UserComponent = {
      id: uniqueId || `user_component_json_${Date.now()}`,
      name: name || `自定义组件${userComponents.value.length + 1}`,
      svgData: '', // JSON方式不需要SVG数据
      jsonData: jsonData, // 保留原始X6数据用于兼容
      componentInfo: componentInfo, // 新的组件信息
      renderType: 'json',
      createdAt: Date.now()
    };
    
    userComponents.value.push(component);
    
    // 保存到localStorage
    try {
      localStorage.setItem('userComponents', JSON.stringify(userComponents.value));
      // 验证是否保存成功
      const saved = localStorage.getItem('userComponents');
      if (!saved) {
        throw new Error('保存失败，localStorage可能已满');
      }
    } catch (error) {
      console.error('保存用户组件到localStorage失败:', error);
      // 如果保存失败，从数组中移除该组件
      const index = userComponents.value.findIndex(comp => comp.id === component.id);
      if (index > -1) {
        userComponents.value.splice(index, 1);
      }
      return null;
    }
    
    return component;
  }

  // 从X6图数据中提取组合组件的完整信息
  function extractComponentInfoFromGraph(jsonData: any, componentName: string): ComponentInfo {
    const pins: ComponentPin[] = [];
    const internalNodes: any[] = [];
    const internalEdges: any[] = [];
    
    console.log('开始提取组合组件信息:', componentName);
    console.log('原始JSON数据:', jsonData);
    
    if (jsonData.cells) {
      jsonData.cells.forEach((cell: any) => {
        if (cell.shape === 'edge') {
          // 提取边信息
          internalEdges.push({
            id: cell.id,
            source: cell.source,
            target: cell.target,
            attrs: cell.attrs,
            labels: cell.labels,
            vertices: cell.vertices,
            connector: cell.connector,
            router: cell.router
          });
        } else if (cell.shape === 'custom-pin') {
          // 提取引脚信息 - 这些将成为组合组件的外部接口
          pins.push({
            id: cell.id,
            type: cell.data?.pinType || 'input',
            label: cell.data?.label || 'PIN',
            position: {
              x: cell.position?.x || 0,
              y: cell.position?.y || 0
            },
            showLabel: true,
            style: cell.data?.style || {},
            attachedToNodeId: cell.data?.attachedToNodeId,
            // 保存原始的cell信息，用于后续的连接映射
            originalCell: cell
          });
        } else {
          // 提取内部节点信息 - 这些是组合组件的内部结构
          internalNodes.push({
            id: cell.id,
            shape: cell.shape,
            position: cell.position,
            size: cell.size,
            attrs: cell.attrs,
            data: cell.data,
            ports: cell.ports,
            zIndex: cell.zIndex
          });
        }
      });
    }
    
    console.log(`提取完成: ${internalNodes.length}个内部节点, ${internalEdges.length}条内部边, ${pins.length}个外部引脚`);
    
    return {
      name: componentName,
      pins: pins,
      nodes: internalNodes, // 内部节点
      edges: internalEdges, // 内部连线
      style: {
        // 可以从主要节点中提取样式信息
      },
      // 保存原始的图数据，用于调试
      originalJsonData: jsonData
    };
  }

  // 从localStorage加载用户组件
  function loadUserComponents() {
    try {
      const saved = localStorage.getItem('userComponents');
      if (saved) {
        userComponents.value = JSON.parse(saved);
      }
    } catch (error) {
      console.error('从localStorage加载用户组件失败:', error);
      userComponents.value = [];
    }
  }

  // 删除用户组件
  function deleteUserComponent(id: string) {
    const index = userComponents.value.findIndex(comp => comp.id === id);
    if (index > -1) {
      userComponents.value.splice(index, 1);
      try {
        localStorage.setItem('userComponents', JSON.stringify(userComponents.value));
      } catch (error) {
        console.error('更新localStorage失败:', error);
      }
    }
  }

  // 初始化时加载用户组件
  loadUserComponents();

  return {
    initJSON,
    currentNode,
    userComponents,
    currentNodeChange,
    initJSONChange,
    saveUserComponent,
    saveUserComponentAsJson, // 新增JSON保存函数
    extractComponentInfoFromGraph, // 新增提取函数
    loadUserComponents,
    deleteUserComponent
  };
});
