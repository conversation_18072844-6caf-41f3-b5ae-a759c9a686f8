import { defineStore } from 'pinia';
import { v4 as uuidv4 } from 'uuid';
import { getComponentList } from '@/api'; 

export interface ComponentData {
  id: string;
  definition: any;
  diagram: any;
}

const getComponentsFromStorage = (): ComponentData[] => {
  const components = localStorage.getItem('customComponents');
  return components ? JSON.parse(components) : [];
};

const saveComponentsToStorage = (components: ComponentData[]) => {
  localStorage.setItem('customComponents', JSON.stringify(components));
};

export const useComponentStore = defineStore('component', {
  state: () => ({
    components: [] as ComponentData[], // 初始为空，通过fetch获取
  }),
  actions: {
    async fetchComponents() {
      try {
        const response = await getComponentList(); // 调用API
        this.components = response.data; // 更新state
        saveComponentsToStorage(this.components); // （可选）如果希望继续缓存到本地
      } catch (error) {
        console.error('获取元件列表失败:', error);
      }
    },
    addComponent(component: Omit<ComponentData, 'id'>) {
      const newComponent = { ...component, id: uuidv4() };
      this.components.push(newComponent);
      saveComponentsToStorage(this.components);
    },
    updateComponent(updatedComponent: ComponentData) {
      const index = this.components.findIndex(c => c.id === updatedComponent.id);
      if (index !== -1) {
        this.components[index] = updatedComponent;
        saveComponentsToStorage(this.components);
      }
    },
    deleteComponent(componentId: string) {
      this.components = this.components.filter(c => c.id !== componentId);
      saveComponentsToStorage(this.components);
    },
    getComponentById(id: string): ComponentData | undefined {
      return this.components.find(c => c.id === id);
    },
  },
});