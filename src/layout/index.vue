<template>
  <el-container class="layout-container">
    <!-- 左侧菜单 -->
    <el-aside class="left-menu" :width="isCollapsed ? '64px' : '240px'">
      <!-- Logo和标题 -->
      <div class="menu-header" :class="{ collapsed: isCollapsed }">
        <img src="@/assets/logo.png" alt="logo" class="menu-logo" />
        <span class="menu-title" v-show="!isCollapsed">PS model</span>
      </div>
      
      <!-- 菜单列表 -->
      <el-menu
        :default-active="$route.path"
        class="el-menu-vertical"
        router
        :collapse="isCollapsed"
      >
        <el-menu-item index="/home">
          <el-icon><HomeFilled /></el-icon>
          <span>首页</span>
        </el-menu-item>
        <el-menu-item index="/simulation">
          <el-icon><Box /></el-icon>
          <span>仿真建模</span>
        </el-menu-item>
        <el-menu-item index="/material">
          <el-icon><Files /></el-icon>
          <span>素材中心</span>
        </el-menu-item>
        <!-- <el-menu-item index="/component-editor">
          <el-icon><Tools /></el-icon>
          <span>元件编辑器</span>
        </el-menu-item> -->
      </el-menu>
      
      <!-- 底部设置按钮和收缩按钮 -->
      <div class="menu-footer">
        <el-button text class="setting-btn">
          <el-icon><Setting /></el-icon>
          <span v-show="!isCollapsed">设置</span>
        </el-button>
        <el-button text class="collapse-btn" @click="isCollapsed = !isCollapsed">
          <el-icon v-if="!isCollapsed"><ArrowLeft /></el-icon>
          <el-icon v-else><ArrowRight /></el-icon>
        </el-button>
      </div>
    </el-aside>
    
    <!-- 右侧主体 -->
    <el-container direction="vertical">
      <!-- 顶部栏 -->
      <el-header class="top-header">
        <div class="header-content">
          <div class="header-left">
            <!-- 可以放置面包屑或页面标题 -->
          </div>
          <div class="header-right">
            <el-dropdown>
              <span class="user-dropdown">
                <el-avatar :size="32" icon="UserFilled" />
                <span class="user-name">{{ userName }}</span>
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>个人中心</el-dropdown-item>
                  <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>
      
      <!-- 内容区域 -->
      <el-main class="content-area">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { HomeFilled, Box, Files, Setting, ArrowDown, ArrowLeft, ArrowRight, Tools } from '@element-plus/icons-vue';

const router = useRouter();
const userName = ref('仿真之王');
const isCollapsed = ref(true);

const handleLogout = () => {
  ElMessage.success('退出登录成功');
  // 清除用户信息
  localStorage.removeItem('userInfo');
  localStorage.removeItem('token');
  // 跳转到登录页
  router.push('/login');
};
</script>

<style scoped>
.layout-container {
  height: 100vh;
  overflow: hidden;
}

/* 左侧菜单样式 */
.left-menu {
  background-color: #001529;
  color: #fff;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  position: relative;
  transition: width 0.2s cubic-bezier(.4,0,.2,1);
}

.menu-header {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: padding 0.2s;
}
.menu-header.collapsed {
  justify-content: center;
  padding: 0;
}

.menu-logo {
  width: 32px;
  height: 32px;
  margin-right: 12px;
  transition: margin 0.2s;
}
.menu-header.collapsed .menu-logo {
  margin-right: 0;
}

/* 炫酷动画：旋转+发光 */
.animated-logo {
  animation: logo-spin 2.5s linear infinite;
  filter: drop-shadow(0 0 8px #40a9ff) drop-shadow(0 0 16px #1890ff);
  transition: filter 0.3s;
}
.animated-logo:hover {
  filter: drop-shadow(0 0 16px #fff) drop-shadow(0 0 32px #40a9ff);
}
@keyframes logo-spin {
  0% { transform: rotate(0deg) scale(1); }
  80% { transform: rotate(360deg) scale(1.08); }
  100% { transform: rotate(360deg) scale(1); }
}

.menu-title {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  transition: opacity 0.2s;
}

.el-menu-vertical {
  flex: 1;
  border-right: none;
  background-color: transparent;
  overflow-y: auto;
}

.el-menu-vertical::-webkit-scrollbar {
  width: 6px;
}

.el-menu-vertical::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.el-menu-vertical::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.el-menu-item {
  color: rgba(255, 255, 255, 0.85);
  height: 48px;
  line-height: 48px;
  margin: 4px 8px;
  border-radius: 4px;
}

.el-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.08);
  color: #fff;
}

.el-menu-item.is-active {
  background-color: #1890ff;
  color: #fff;
}

.el-menu-item .el-icon {
  margin-right: 10px;
  font-size: 18px;
}

.menu-footer {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0 16px 0;
  background: linear-gradient(to top, rgba(0,21,41,0.95) 80%, rgba(0,21,41,0.5) 100%, transparent 100%);
  box-sizing: border-box;
}

.setting-btn {
  width: 80%;
  justify-content: center;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 8px;
}

.setting-btn:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.08);
}

.collapse-btn {
  width: 40px;
  height: 40px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  border-radius: 50%;
  transition: background 0.2s;
}
.collapse-btn:hover {
  background: rgba(255,255,255,0.08);
}

/* 顶部栏样式 */
.top-header {
  height: 60px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 0;
}

.header-content {
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #606266;
  gap: 8px;
}

.user-dropdown:hover {
  color: #1890ff;
}

.user-name {
  font-size: 14px;
}

/* 内容区域样式 */
.content-area {
  background-color: #f5f5f5;
  padding: 0;
  overflow: hidden;
}

/* 深色主题下的图标颜色修正 */
.el-menu-item .el-icon {
  color: inherit;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .left-menu {
    width: 60px !important;
  }
  
  .menu-title,
  .el-menu-item span:not(.el-icon),
  .setting-btn span:not(.el-icon),
  .help-btn span:not(.el-icon) {
    display: none;
  }
  
  .menu-header {
    justify-content: center;
    padding: 0;
  }
  
  .menu-logo {
    margin-right: 0;
  }
  .menu-footer {
    padding: 8px 0 8px 0;
  }
}

/* collapse 状态下菜单图标居中 */
.el-menu--collapse .el-menu-item {
  justify-content: center !important;
  text-align: center;
  padding: 0 !important;
}
.el-menu--collapse .el-menu-item .el-icon {
  margin-right: 0 !important;
}

/* 优化底部按钮 hover 效果 */
.setting-btn:hover,
.collapse-btn:hover {
  background: rgba(255,255,255,0.12);
  color: #fff;
}

/* 收缩时隐藏文字 */
.menu-footer .setting-btn span:not(.el-icon) {
  display: none;
}

/* 收缩时收缩按钮居中 */
.collapse-btn {
  margin: 0 auto;
}

:deep(.el-button.is-text):not(.is-disabled):hover {
  background: rgba(255,255,255,0.07) !important;
  color: #fff !important;
}
</style>