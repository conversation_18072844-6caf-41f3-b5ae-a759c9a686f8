<template>
  <div class="editor-layout">
    <!-- 只包含主要内容区域，不包含左侧菜单 -->
    <main class="editor-main">
      <router-view />
    </main>
  </div>
</template>

<script setup lang="ts">
// 简洁的编辑器布局，专门用于组件编辑器页面
</script>

<style scoped>
.editor-layout {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.editor-main {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>