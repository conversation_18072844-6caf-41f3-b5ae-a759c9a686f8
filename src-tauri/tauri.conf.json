{"$schema": "https://schema.tauri.app/config/2", "productName": "psmodel", "version": "0.1.0", "identifier": "com.psmodel.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "psmodel", "width": 1400, "height": 900, "minWidth": 1200, "minHeight": 800, "resizable": true, "maximizable": true, "center": true, "devtools": true}], "security": {"csp": null}, "withGlobalTauri": true}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}