// 1. 使用 #[path] 属性来显式指定模块的路径, 并将其声明为公共
#[path = "../api/mod.rs"]
pub mod api;

use serde::{Deserialize, Serialize};
use std::sync::{Arc, Mutex};
use tauri::{Manager, State};

// 定义一个状态结构体来保存端口号
pub struct PortState(pub Arc<Mutex<Option<u16>>>);

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
async fn get_backend_port(state: State<'_, PortState>) -> Result<u16, String> {
    // 尝试获取端口号，最多等待10秒
    for _ in 0..100 {
        if let Some(port) = *state.0.lock().unwrap() {
            return Ok(port);
        }
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    }
    Err("Failed to get backend port in time".to_string())
}

// 定义文件信息返回结构体
#[derive(Serialize, Deserialize, Debug)]
pub struct FileInfo {
    pub file_name: String,
    pub file_path: String,
    pub file_size: u64,
    pub file_content: String, // base64 编码
    pub mime_type: Option<String>,
}

// 文件大小限制 (10MB)
const MAX_FILE_SIZE: u64 = 10 * 1024 * 1024;

#[tauri::command]
pub async fn file_picker() -> Result<String, String> {
    Ok("Test function works".to_string())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let port_state = PortState(Arc::new(Mutex::new(None)));

    tauri::Builder::default()
        .manage(port_state)
        .setup(|app| {
            let app_handle = app.handle().clone();
            let port_state = app_handle.state::<PortState>();
            let port_clone = Arc::clone(&port_state.0);

            tauri::async_runtime::spawn(async move {
                let current_dir = std::env::current_dir().expect("Failed to get current directory");
                let path = current_dir;
                let port = api::start_server(false, path).await;

                // 将端口号存储在状态中
                let mut port_guard = port_clone.lock().unwrap();
                *port_guard = Some(port);
            });
            Ok(())
        })
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_http::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            get_backend_port,
            file_picker
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
