// 1. 引入所有需要的模块和类型
use std::sync::Arc;
use axum::{
    body::Body,
    http::{Request, Response},
    middleware::{self, Next},
    Router,
};
use tower::ServiceBuilder;
use tower_http::services::ServeDir;
use tracing::info;

// 从 psmodel_lib 中引入需要的模块
// 注意：web.rs 是一个 bin，它可以访问 lib crate (psmodel_lib) 的公共 API
use psmodel_lib::api::{
    database::setup_lmdb_environment,
    log,
    router::api_v1_component,
    utils,
    AppState,
};

// 2. 复制非 pub 的中间件函数
async fn preprocess_middleware(request: Request<Body>, next: Next) -> Response<Body> {
    info!("Preprocessing request: {} {}", request.method(), request.uri());
    let response = next.run(request).await;
    info!("Finished processing request.");
    response
}

#[tokio::main]
async fn main() {
    // 3. 将 start_server 的逻辑内联到 main 函数中
    let current_dir = std::env::current_dir().expect("Failed to get current directory");
    
    let app_data_dir = current_dir;

    // 初始化日志
    let _guards = log::init(app_data_dir.clone());

    // 初始化数据库
    info!("Initializing LMDB database...");
    let (env, db) = setup_lmdb_environment(app_data_dir).expect("实例化LMDB数据库失败");
    info!("LMDB database initialized successfully.");
    
    // 创建应用状态
    info!("Creating application state...");
    let state = Arc::new(AppState {
        env: Arc::new(env),
        db,
    });
    info!("Application state created successfully.");

    // 构建API路由
    info!("Building API routes...");
    let component_router = api_v1_component();
    let v1_router = Router::new().nest("/v1/component", component_router);
    
    // 将所有 v1 路由组合到 /api 下，并应用中间件
    let api_router = Router::new()
        .nest("/api", v1_router)
        .layer(
            ServiceBuilder::new()
                .layer(middleware::from_fn(preprocess_middleware))
        );
    info!("API routes built successfully.");

    // 组合路由并添加状态
    // 在 web 模式下，我们总是提供静态文件服务
    info!("Serving static files from 'dist' directory.");
    let static_files_service = ServeDir::new("dist")
        .not_found_service(ServeDir::new("dist/index.html"));

    // 同时提供API和静态文件
    // 使用 merge 将 api_router 的路由合并进来, 并统一提供 state
    let app = Router::new()
        .merge(api_router)
        .fallback_service(static_files_service)
        .with_state(state);

    // 寻找可用端口并启动服务器
    let port = utils::find_available_port(7046).await;
    let addr = format!("0.0.0.0:{}", port);
    let listener = tokio::net::TcpListener::bind(&addr).await.unwrap();
    
    println!("Axum server is running at http://{}", addr);
    info!("Axum server listening on {}", addr);
    
    // 运行服务器，这将阻塞 main 函数直到服务器停止
    axum::serve(listener, app).await.unwrap();
}