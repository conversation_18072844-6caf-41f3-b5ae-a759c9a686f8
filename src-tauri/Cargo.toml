[package]
name = "psmodel"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"
default-run = "psmodel"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[[bin]]
name = "psmodel"
path = "src/main.rs"

[[bin]]
name = "web"
path = "src/bin/web.rs"

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "psmodel_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = ["devtools"] }
tauri-plugin-http = "2"
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"

axum = { version = "0.8.4", features = ["macros"] }
tokio = { version = "1", features = ["macros", "rt-multi-thread", "signal"] }
tower = "0.5.2"
tower-http = { version = "0.6.6", features = ["fs", "cors"] }
anyhow = "1.0"
uuid = { version = "1.6", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
lmdb = "0.8.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-rolling = "0.3"
tracing-appender = "0.2"
