use std::sync::Arc;
use chrono::Local;
use axum::extract::{Query, State};
use axum::Json;
use lmdb::Transaction;
use crate::api::AppState;
use crate::api::config::{COMPONENT_KEY, DIAGRAM_KEY};
use crate::api::data::base::ApiResponse;
use crate::api::data::component::{GetDiagramParams, SaveDiagramParams};
use crate::api::handler::component::Component;
use crate::api::utils::{with_transaction, BusinessError};

// 保存图
#[axum::debug_handler]
pub async fn save_diagram(
    State(state): State<Arc<AppState>>,
    Json(payload): Json<SaveDiagramParams>,
) -> Json<ApiResponse> {
    let result = with_transaction(&state, |txn, db| {
        // 1. 读取并反序列化元件列表
        let mut components: Vec<Component> =
            match txn.get(db, &COMPONENT_KEY.as_bytes()) {
                Ok(data) => serde_json::from_slice(data)
                    .map_err(|e| BusinessError::Internal(format!("组件数据反序列化失败: {}", e)))?,
                Err(lmdb::Error::NotFound) => vec![],
                Err(e) => return Err(BusinessError::Database(e.to_string())),
            };

        // 2. 检查关键字是否与除自己以外的其他元件重复
        if components.iter().any(|c| {
            c.id != payload.component_id && c.keyword == payload.definition.keyword
        }) {
            return Err(BusinessError::Validation("关键字已重复".to_string()));
        }

        // 3. 查找需要更新的元件，并同步信息
        let component_to_update = components
            .iter_mut()
            .find(|c| c.id == payload.component_id);

        match component_to_update {
            Some(c) => {
                c.name = payload.definition.name.clone();
                c.keyword = payload.definition.keyword.clone();
                c.description = payload.definition.description.clone();
                c.update_time = Local::now().format("%Y-%m-%d %H:%M:%S").to_string();
            }
            None => return Err(BusinessError::Validation("组件ID不存在".to_string())),
        }

        // 4. 将更新后的元件列表序列化并写回数据库
        let json_components = serde_json::to_vec(&components)
            .map_err(|e| BusinessError::Internal(format!("序列化组件失败: {}", e)))?;
        txn.put(
            db,
            &COMPONENT_KEY.as_bytes(),
            &json_components,
            lmdb::WriteFlags::empty(),
        )
        .map_err(|e| BusinessError::Database(format!("存储组件数据失败: {}", e)))?;


        // 5. 保存图数据
        let diagrams_data = txn.get(db, &DIAGRAM_KEY.as_bytes());
        let mut diagrams: serde_json::Value = match diagrams_data {
            Ok(data) => serde_json::from_slice(data)
                .map_err(|e| BusinessError::Internal(format!("图数据反序列化失败: {}", e)))?,
            Err(lmdb::Error::NotFound) => serde_json::json!({}),
            Err(e) => return Err(BusinessError::Database(e.to_string())),
        };

        let now_time = Local::now().format("%Y-%m-%d %H:%M:%S").to_string();

        let entry = diagrams.as_object_mut().unwrap().entry(payload.component_id.clone());
        let diagram_entry = entry.or_insert(serde_json::json!({}));
        let diagram_obj = diagram_entry.as_object_mut().unwrap();

        // 将新的 definition 结构体转换为 JSON Value
        let definition_value = serde_json::to_value(&payload.definition)
            .map_err(|e| BusinessError::Internal(format!("序列化 definition 失败: {}", e)))?;

        diagram_obj.insert("definition".to_string(), definition_value);
        diagram_obj.insert("diagram".to_string(), payload.diagram);
        diagram_obj.insert("property".to_string(), payload.property);
        diagram_obj.insert("update_time".to_string(), serde_json::Value::String(now_time.clone()));
        if !diagram_obj.contains_key("create_time") {
            diagram_obj.insert("create_time".to_string(), serde_json::Value::String(now_time.clone()));
        }

        let json_diagrams = serde_json::to_vec(&diagrams)
            .map_err(|e| BusinessError::Internal(format!("序列化图数据失败: {}", e)))?;

        txn.put(
            db,
            &DIAGRAM_KEY.as_bytes(),
            &json_diagrams,
            lmdb::WriteFlags::empty(),
        )
        .map_err(|e| BusinessError::Database(format!("存储图数据失败: {}", e)))?;

        Ok(())
    });

    match result {
        Ok(_) => ApiResponse::success_with_message(serde_json::json!({}), "保存成功").to_json(),
        Err(e) => e.into(),
    }
}

// 获取图
#[axum::debug_handler]
pub async fn get_diagram(
    State(state): State<Arc<AppState>>,
    Query(params): Query<GetDiagramParams>,
) -> Json<ApiResponse> {
    let txn = match state.env.begin_ro_txn() {
        Ok(txn) => txn,
        Err(e) => return ApiResponse::error(500, format!("事务初始化失败: {}", e)).to_json(),
    };

    let db = state.db;

    // 校验 component_id 是否存在
    let components: Vec<Component> = match txn.get(db, &COMPONENT_KEY.as_bytes()) {
        Ok(data) => serde_json::from_slice(data).unwrap_or_else(|_| vec![]),
        Err(_) => vec![],
    };

    if !components.iter().any(|c| c.id == params.component_id) {
        return ApiResponse::error(1002, "组件ID不存在").to_json();
    }

    let diagrams: serde_json::Value = match txn.get(db, &DIAGRAM_KEY.as_bytes()) {
        Ok(data) => serde_json::from_slice(data).unwrap_or_else(|_| serde_json::json!({})),
        Err(_) => serde_json::json!({}),
    };

    let empty_diagram = serde_json::json!({});
    let diagram = diagrams.get(&params.component_id).unwrap_or(&empty_diagram).clone();

    ApiResponse::success(diagram).to_json()
}