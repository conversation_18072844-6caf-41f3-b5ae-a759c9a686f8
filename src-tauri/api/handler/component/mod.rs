use crate::api::config::{
    COMPONENT_KEY, COMPONENT_TYPE_LIST, CUSTOM_COMPONENT_TYPE_KEY, DIAGRAM_KEY,
};
use crate::api::data::base::ApiResponse;
use crate::api::data::component::{
    AddComponentParams, AddComponentTypeParams, DeleteComponentParams, DeleteComponentTypeParams,
    GetComponentParams, ListComponentParams, UpdateComponentParams, UpdateComponentTypeParams,
};
use crate::api::utils::{with_transaction, BusinessError};
use crate::api::AppState;
use axum::extract::{Query, State};
use axum::Json;
use chrono::Local;
use lmdb::Transaction;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use uuid::Uuid;

// 定义完整的元件信息结构体
#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct Component {
    pub id: String,
    pub name: String,
    pub keyword: String,
    pub component_type_id: i32,
    pub description: String,
    #[serde(default)]
    pub remark: Option<String>,
    pub create_time: String,
    pub update_time: String,
}

// 定义用于返回的带有图信息的元件结构体
#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct ComponentWithDiagram {
    #[serde(flatten)]
    pub component: Component,
    pub is_diagram: i32,
}

// 定义自定义元件类型结构体
#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct CustomComponentType {
    pub id: i32,
    pub name: String,
    #[serde(default)]
    pub remark: Option<String>,
}

// 定义用于返回的统一元件类型结构体
#[derive(Serialize, Clone, Debug)]
pub struct CombinedComponentType {
    pub id: i32,
    pub name: String,
    #[serde(default)]
    pub remark: Option<String>,
    pub is_origin: i32,
}

// 获取元件类型列表（合并内置和自定义）
pub async fn get_component_type_list(State(state): State<Arc<AppState>>) -> Json<ApiResponse> {
    let txn = match state.env.begin_ro_txn() {
        Ok(txn) => txn,
        Err(e) => return ApiResponse::error(500, format!("事务初始化失败: {}", e)).to_json(),
    };

    // 1. 获取内置类型
    let mut combined_list: Vec<CombinedComponentType> = COMPONENT_TYPE_LIST
        .iter()
        .map(|ct| CombinedComponentType {
            id: ct.id,
            name: ct.name.to_string(),
            remark: None, // Built-in types have no remark
            is_origin: 1,
        })
        .collect();

    // 2. 获取自定义类型
    let custom_types_result: Result<Vec<CustomComponentType>, lmdb::Error> =
        match txn.get(state.db, &CUSTOM_COMPONENT_TYPE_KEY.as_bytes()) {
            Ok(data) => serde_json::from_slice(data).map_err(|_| lmdb::Error::Corrupted),
            Err(lmdb::Error::NotFound) => Ok(vec![]),
            Err(e) => Err(e),
        };

    match custom_types_result {
        Ok(custom_types) => {
            let custom_combined_types: Vec<CombinedComponentType> = custom_types
                .into_iter()
                .map(|ct| CombinedComponentType {
                    id: ct.id,
                    name: ct.name,
                    remark: ct.remark,
                    is_origin: 2,
                })
                .collect();
            combined_list.extend(custom_combined_types);
            ApiResponse::success(combined_list).to_json()
        }
        Err(e) => ApiResponse::error(500, format!("无法从数据库读取自定义组件类型: {}", e)).to_json(),
    }
}

// 新建自定义元件类型
#[axum::debug_handler]
pub async fn add_component_type(
    State(state): State<Arc<AppState>>,
    Json(payload): Json<AddComponentTypeParams>,
) -> Json<ApiResponse> {
    let result = with_transaction(&state, |txn, db| {
        let mut custom_types: Vec<CustomComponentType> =
            match txn.get(db, &CUSTOM_COMPONENT_TYPE_KEY.as_bytes()) {
                Ok(data) => serde_json::from_slice(data).map_err(|e| {
                    BusinessError::Internal(format!("自定义元件类型数据反序列化失败: {}", e))
                })?,
                Err(lmdb::Error::NotFound) => vec![],
                Err(e) => return Err(BusinessError::Database(e.to_string())),
            };

        // 检查名称是否重复
        if custom_types.iter().any(|ct| ct.name == payload.name) {
            return Err(BusinessError::Validation("元件类型名称已存在".to_string()));
        }

        // 生成新的ID，从16开始，或者在现有最大ID上加1
        let new_id = custom_types.iter().map(|ct| ct.id).max().unwrap_or(15) + 1;

        let new_type = CustomComponentType {
            id: new_id,
            name: payload.name,
            remark: payload.remark.clone(),
        };

        custom_types.push(new_type.clone());

        let json_types = serde_json::to_vec(&custom_types)
            .map_err(|e| BusinessError::Internal(format!("序列化失败: {}", e)))?;

        txn.put(
            db,
            &CUSTOM_COMPONENT_TYPE_KEY.as_bytes(),
            &json_types,
            lmdb::WriteFlags::empty(),
        )
        .map_err(|e| BusinessError::Database(format!("数据存储失败: {}", e)))?;

        Ok(new_type)
    });

    match result {
        Ok(component_type) => {
            ApiResponse::success_with_message(component_type, "新建成功").to_json()
        }
        Err(e) => e.into(),
    }
}

// 更新自定义元件类型
#[axum::debug_handler]
pub async fn update_component_type(
    State(state): State<Arc<AppState>>,
    Json(payload): Json<UpdateComponentTypeParams>,
) -> Json<ApiResponse> {
    if payload.id <= 15 {
        return BusinessError::Validation("不能修改内置的元件类型".to_string()).into();
    }

    let result = with_transaction(&state, |txn, db| {
        let mut custom_types: Vec<CustomComponentType> =
            match txn.get(db, &CUSTOM_COMPONENT_TYPE_KEY.as_bytes()) {
                Ok(data) => serde_json::from_slice(data).map_err(|e| {
                    BusinessError::Internal(format!("自定义元件类型数据反序列化失败: {}", e))
                })?,
                Err(lmdb::Error::NotFound) => vec![],
                Err(e) => return Err(BusinessError::Database(e.to_string())),
            };

        // 检查名称是否与其他自定义类型重复
        if custom_types
            .iter()
            .any(|ct| ct.name == payload.name && ct.id != payload.id)
        {
            return Err(BusinessError::Validation("元件类型名称已存在".to_string()));
        }

        let component_type = match custom_types.iter_mut().find(|ct| ct.id == payload.id) {
            Some(ct) => ct,
            None => return Err(BusinessError::Validation("未找到要更新的元件类型".to_string())),
        };

        component_type.name = payload.name;
        component_type.remark = payload.remark;

        let json_types = serde_json::to_vec(&custom_types)
            .map_err(|e| BusinessError::Internal(format!("序列化失败: {}", e)))?;

        txn.put(
            db,
            &CUSTOM_COMPONENT_TYPE_KEY.as_bytes(),
            &json_types,
            lmdb::WriteFlags::empty(),
        )
        .map_err(|e| BusinessError::Database(format!("数据存储失败: {}", e)))?;

        Ok(())
    });

    match result {
        Ok(_) => ApiResponse::success_with_message(serde_json::json!({}), "更新成功").to_json(),
        Err(e) => e.into(),
    }
}

// 删除自定义元件类型
#[axum::debug_handler]
pub async fn delete_component_type(
    State(state): State<Arc<AppState>>,
    Json(payload): Json<DeleteComponentTypeParams>,
) -> Json<ApiResponse> {
    if payload.id <= 15 {
        return BusinessError::Validation("不能删除内置的元件类型".to_string()).into();
    }

    let result = with_transaction(&state, |txn, db| {
        let mut custom_types: Vec<CustomComponentType> =
            match txn.get(db, &CUSTOM_COMPONENT_TYPE_KEY.as_bytes()) {
                Ok(data) => serde_json::from_slice(data).map_err(|e| {
                    BusinessError::Internal(format!("自定义元件类型数据反序列化失败: {}", e))
                })?,
                Err(lmdb::Error::NotFound) => vec![],
                Err(e) => return Err(BusinessError::Database(e.to_string())),
            };

        let initial_len = custom_types.len();
        custom_types.retain(|ct| ct.id != payload.id);

        if custom_types.len() == initial_len {
            return Err(BusinessError::Validation("未找到要删除的元件类型".to_string()));
        }

        let json_types = serde_json::to_vec(&custom_types)
            .map_err(|e| BusinessError::Internal(format!("序列化失败: {}", e)))?;

        txn.put(
            db,
            &CUSTOM_COMPONENT_TYPE_KEY.as_bytes(),
            &json_types,
            lmdb::WriteFlags::empty(),
        )
        .map_err(|e| BusinessError::Database(format!("数据存储失败: {}", e)))?;

        Ok(())
    });

    match result {
        Ok(_) => ApiResponse::success_with_message(serde_json::json!({}), "删除成功").to_json(),
        Err(e) => e.into(),
    }
}

// 新建元件
#[axum::debug_handler]
pub async fn add_component(
    State(state): State<Arc<AppState>>,
    Json(payload): Json<AddComponentParams>,
) -> Json<ApiResponse> {
    let result = with_transaction(&state, |txn, db| {
        // 校验元件类型ID是否存在
        let custom_types: Vec<CustomComponentType> =
            match txn.get(db, &CUSTOM_COMPONENT_TYPE_KEY.as_bytes()) {
                Ok(data) => serde_json::from_slice(data)
                    .map_err(|e| BusinessError::Internal(format!("自定义元件类型数据反序列化失败: {}", e)))?,
                Err(lmdb::Error::NotFound) => vec![],
                Err(e) => return Err(BusinessError::Database(e.to_string())),
            };

        let is_valid_type = COMPONENT_TYPE_LIST
            .iter()
            .any(|ct| ct.id == payload.component_type_id)
            || custom_types
                .iter()
                .any(|ct| ct.id == payload.component_type_id);

        if !is_valid_type {
            return Err(BusinessError::Validation("无效的元件类型ID".to_string()));
        }

        let components_data = txn.get(db, &COMPONENT_KEY.as_bytes());
        let mut components: Vec<Component> = match components_data {
            Ok(data) => serde_json::from_slice(data)
                .map_err(|e| BusinessError::Internal(format!("组件数据反序列化失败: {}", e)))?,
            Err(lmdb::Error::NotFound) => vec![],
            Err(e) => return Err(BusinessError::Database(e.to_string())),
        };

        if components.iter().any(|c| c.keyword == payload.keyword) {
            return Err(BusinessError::Validation("关键字已重复".to_string()));
        }

        let uuid = Uuid::new_v4().to_string();
        let now_time = Local::now().format("%Y-%m-%d %H:%M:%S").to_string();

        let component = Component {
            id: uuid.clone(),
            name: payload.name,
            keyword: payload.keyword,
            component_type_id: payload.component_type_id,
            description: payload.description,
            remark: payload.remark,
            create_time: now_time.clone(),
            update_time: now_time,
        };

        components.push(component.clone());

        let json_components = serde_json::to_vec(&components)
            .map_err(|e| BusinessError::Internal(format!("序列化失败: {}", e)))?;

        txn.put(
            db,
            &COMPONENT_KEY.as_bytes(),
            &json_components,
            lmdb::WriteFlags::empty(),
        )
        .map_err(|e| BusinessError::Database(format!("数据存储失败: {}", e)))?;

        Ok(component)
    });

    match result {
        Ok(component) => ApiResponse::success_with_message(component, "保存成功").to_json(),
        Err(e) => e.into(),
    }
}

// 获取元件列表
#[axum::debug_handler]
pub async fn list_component(
    State(state): State<Arc<AppState>>,
    Query(params): Query<ListComponentParams>,
) -> Json<ApiResponse> {
    let txn = match state.env.begin_ro_txn() {
        Ok(txn) => txn,
        Err(e) => return ApiResponse::error(500, format!("事务初始化失败: {}", e)).to_json(),
    };

    let db = state.db;
    match txn.get(db, &COMPONENT_KEY.as_bytes()) {
        Ok(data) => {
            let components: Vec<Component> =
                serde_json::from_slice(data).unwrap_or_else(|_| vec![]);

            let diagrams: serde_json::Value = match txn.get(db, &DIAGRAM_KEY.as_bytes()) {
                Ok(diagram_data) => {
                    serde_json::from_slice(diagram_data).unwrap_or_else(|_| serde_json::json!({}))
                }
                Err(_) => serde_json::json!({}),
            };

            let diagrams_obj = diagrams.as_object();

            let components_with_diagram: Vec<ComponentWithDiagram> = components
                .into_iter()
                .map(|c| {
                    let is_diagram = if let Some(obj) = diagrams_obj {
                        if obj.contains_key(&c.id) {
                            1 // 1: 表示元件存在对应的图数据
                        } else {
                            2 // 2: 表示元件不存在对应的图数据
                        }
                    } else {
                        2 // 2: 默认表示元件不存在对应的图数据
                    };
                    ComponentWithDiagram {
                        component: c,
                        is_diagram,
                    }
                })
                .collect();

            let filtered_components: Vec<ComponentWithDiagram> = match params.component_type_id {
                Some(type_id) => components_with_diagram
                    .into_iter()
                    .filter(|c| c.component.component_type_id == type_id)
                    .collect(),
                None => components_with_diagram
                    .into_iter()
                    .filter(|c| c.component.component_type_id == 1) // 默认只显示节点类型
                    .collect(),
            };
            ApiResponse::success(filtered_components).to_json()
        }
        Err(_) => {
            let components: Vec<ComponentWithDiagram> = vec![];
            ApiResponse::success(components).to_json()
        }
    }
}

// 删除元件
#[axum::debug_handler]
pub async fn delete_component(
    State(state): State<Arc<AppState>>,
    Json(payload): Json<DeleteComponentParams>,
) -> Json<ApiResponse> {
    let result = with_transaction(&state, |txn, db| {
        let components_data = txn.get(db, &COMPONENT_KEY.as_bytes());
        let mut components: Vec<Component> = match components_data {
            Ok(data) => serde_json::from_slice(data)
                .map_err(|e| BusinessError::Internal(format!("组件数据反序列化失败: {}", e)))?,
            Err(lmdb::Error::NotFound) => vec![],
            Err(e) => return Err(BusinessError::Database(e.to_string())),
        };

        let initial_len = components.len();
        components.retain(|c| c.id != payload.component_id);

        if components.len() == initial_len {
            return Err(BusinessError::Validation("未找到要删除的元件".to_string()));
        }

        let json_components = serde_json::to_vec(&components)
            .map_err(|e| BusinessError::Internal(format!("序列化失败: {}", e)))?;

        txn.put(
            db,
            &COMPONENT_KEY.as_bytes(),
            &json_components,
            lmdb::WriteFlags::empty(),
        )
        .map_err(|e| BusinessError::Database(format!("数据存储失败: {}", e)))?;

        // 删除关联的图数据
        let diagrams_data = txn.get(db, &DIAGRAM_KEY.as_bytes());
        let mut diagrams: serde_json::Value = match diagrams_data {
            Ok(data) => serde_json::from_slice(data)
                .map_err(|e| BusinessError::Internal(format!("图数据反序列化失败: {}", e)))?,
            Err(lmdb::Error::NotFound) => serde_json::json!({}),
            Err(e) => return Err(BusinessError::Database(e.to_string())),
        };

        if let Some(obj) = diagrams.as_object_mut() {
            if obj.remove(&payload.component_id).is_some() {
                let json_diagrams = serde_json::to_vec(&diagrams)
                    .map_err(|e| BusinessError::Internal(format!("序列化图数据失败: {}", e)))?;
                txn.put(
                    db,
                    &DIAGRAM_KEY.as_bytes(),
                    &json_diagrams,
                    lmdb::WriteFlags::empty(),
                )
                .map_err(|e| BusinessError::Database(format!("存储图数据失败: {}", e)))?;
            }
        }

        Ok(())
    });

    match result {
        Ok(_) => ApiResponse::success_with_message(serde_json::json!({}), "删除成功").to_json(),
        Err(e) => e.into(),
    }
}

// 更新元件
#[axum::debug_handler]
pub async fn update_component(
    State(state): State<Arc<AppState>>,
    Json(payload): Json<UpdateComponentParams>,
) -> Json<ApiResponse> {
    let result = with_transaction(&state, |txn, db| {
        // 1. 校验元件类型ID
        let custom_types: Vec<CustomComponentType> =
            match txn.get(db, &CUSTOM_COMPONENT_TYPE_KEY.as_bytes()) {
                Ok(data) => serde_json::from_slice(data)
                    .map_err(|e| BusinessError::Internal(format!("自定义元件类型数据反序列化失败: {}", e)))?,
                Err(lmdb::Error::NotFound) => vec![],
                Err(e) => return Err(BusinessError::Database(e.to_string())),
            };

        let is_valid_type = COMPONENT_TYPE_LIST
            .iter()
            .any(|ct| ct.id == payload.component_type_id)
            || custom_types
                .iter()
                .any(|ct| ct.id == payload.component_type_id);

        if !is_valid_type {
            return Err(BusinessError::Validation("无效的元件类型ID".to_string()));
        }

        // 2. 读取并更新元件列表
        let mut components: Vec<Component> = match txn.get(db, &COMPONENT_KEY.as_bytes()) {
            Ok(data) => serde_json::from_slice(data)
                .map_err(|e| BusinessError::Internal(format!("组件数据反序列化失败: {}", e)))?,
            Err(lmdb::Error::NotFound) => vec![],
            Err(e) => return Err(BusinessError::Database(e.to_string())),
        };

        // 检查关键字是否与除自己以外的其他元件重复
        if components
            .iter()
            .any(|c| c.keyword == payload.keyword && c.id != payload.component_id)
        {
            return Err(BusinessError::Validation("关键字已重复".to_string()));
        }

        let component = match components
            .iter_mut()
            .find(|c| c.id == payload.component_id)
        {
            Some(c) => c,
            None => return Err(BusinessError::Validation("组件ID不存在".to_string())),
        };

        component.name = payload.name.clone();
        component.keyword = payload.keyword.clone();
        component.component_type_id = payload.component_type_id;
        component.description = payload.description.clone().unwrap_or_default();
        component.remark = payload.remark.clone();
        component.update_time = Local::now().format("%Y-%m-%d %H:%M:%S").to_string();

        // 3. 将更新后的元件列表写回数据库
        let json_components = serde_json::to_vec(&components)
            .map_err(|e| BusinessError::Internal(format!("序列化失败: {}", e)))?;

        txn.put(
            db,
            &COMPONENT_KEY.as_bytes(),
            &json_components,
            lmdb::WriteFlags::empty(),
        )
        .map_err(|e| BusinessError::Database(format!("数据存储失败: {}", e)))?;

        // 4. 同步更新关联的图数据
        let mut diagrams: serde_json::Value = match txn.get(db, &DIAGRAM_KEY.as_bytes()) {
            Ok(data) => serde_json::from_slice(data)
                .map_err(|e| BusinessError::Internal(format!("图数据反序列化失败: {}", e)))?,
            Err(lmdb::Error::NotFound) => return Ok(()), // 没有图数据，直接成功返回
            Err(e) => return Err(BusinessError::Database(e.to_string())),
        };

        if let Some(diagram_entry) = diagrams.get_mut(&payload.component_id) {
            if let Some(definition) = diagram_entry.get_mut("definition") {
                if let Some(def_obj) = definition.as_object_mut() {
                    def_obj.insert("name".to_string(), serde_json::Value::String(payload.name.clone()));
                    def_obj.insert("keyword".to_string(), serde_json::Value::String(payload.keyword.clone()));
                    def_obj.insert("description".to_string(), serde_json::Value::String(payload.description.unwrap_or_default()));

                    let json_diagrams = serde_json::to_vec(&diagrams)
                        .map_err(|e| BusinessError::Internal(format!("序列化图数据失败: {}", e)))?;

                    txn.put(
                        db,
                        &DIAGRAM_KEY.as_bytes(),
                        &json_diagrams,
                        lmdb::WriteFlags::empty(),
                    )
                    .map_err(|e| BusinessError::Database(format!("存储图数据失败: {}", e)))?;
                }
            }
        }

        Ok(())
    });

    match result {
        Ok(_) => ApiResponse::success_with_message(serde_json::json!({}), "更新成功").to_json(),
        Err(e) => e.into(),
    }
}

// 获取单个元件信息
#[axum::debug_handler]
pub async fn get_component(
    State(state): State<Arc<AppState>>,
    Query(params): Query<GetComponentParams>,
) -> Json<ApiResponse> {
    let txn = match state.env.begin_ro_txn() {
        Ok(txn) => txn,
        Err(e) => return ApiResponse::error(500, format!("事务初始化失败: {}", e)).to_json(),
    };

    let db = state.db;
    let components: Vec<Component> = match txn.get(db, &COMPONENT_KEY.as_bytes()) {
        Ok(data) => serde_json::from_slice(data).unwrap_or_else(|_| vec![]),
        Err(_) => vec![],
    };

    if let Some(component) = components.into_iter().find(|c| c.id == params.component_id) {
        let diagrams: serde_json::Value = match txn.get(db, &DIAGRAM_KEY.as_bytes()) {
            Ok(diagram_data) => {
                serde_json::from_slice(diagram_data).unwrap_or_else(|_| serde_json::json!({}))
            }
            Err(_) => serde_json::json!({}),
        };

        let diagrams_obj = diagrams.as_object();
        let is_diagram = if let Some(obj) = diagrams_obj {
            if obj.contains_key(&component.id) {
                1
            } else {
                2
            }
        } else {
            2
        };

        let component_with_diagram = ComponentWithDiagram {
            component,
            is_diagram,
        };
        ApiResponse::success(component_with_diagram).to_json()
    } else {
        ApiResponse::error(1006, "组件ID不存在").to_json()
    }
}
