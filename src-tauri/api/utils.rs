use std::sync::Arc;
use axum::Json;
use lmdb::{RwTransaction, Transaction};
use crate::api::AppState;
use crate::api::data::base::ApiResponse;

// 定义一个统一的业务错误类型
#[derive(Debug)]
pub enum BusinessError {
    Validation(String), // 验证错误
    Database(String),   // 数据库操作错误
    Internal(String),   // 内部服务器错误
}

// 将业务错误转换为 ApiResponse
impl From<BusinessError> for Json<ApiResponse> {
    fn from(err: BusinessError) -> Self {
        match err {
            BusinessError::Validation(msg) => ApiResponse::error(1002, &msg).to_json(),
            BusinessError::Database(msg) => ApiResponse::error(500, &format!("数据操作失败: {}", msg)).to_json(),
            BusinessError::Internal(msg) => ApiResponse::error(500, &format!("服务器内部错误: {}", msg)).to_json(),
        }
    }
}

// 这是一个高阶函数，用于处理读写事务
pub fn with_transaction<F, T>(
    state: &Arc<AppState>,
    operation: F,
) -> Result<T, BusinessError>
where
    F: FnOnce(&mut RwTransaction, lmdb::Database) -> Result<T, BusinessError>,
{
    let mut txn = state.env.begin_rw_txn().map_err(|e| BusinessError::Internal(format!("事务初始化失败: {}", e)))?;
    let db = state.db;

    match operation(&mut txn, db) {
        Ok(result) => {
            txn.commit().map_err(|e| BusinessError::Internal(format!("事务提交失败: {}", e)))?;
            Ok(result)
        }
        Err(e) => {
            // 事务会自动在 drop 时中止（回滚）
            Err(e)
        }
    }
}
use tokio::net::TcpListener;

// 寻找可用端口
pub async fn find_available_port(start_port: u16) -> u16 {
    let mut port = start_port;
    while let Err(_) = TcpListener::bind(("0.0.0.0", port)).await {
        port += 1;
    }
    port
}