use axum::Json;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};

#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse {
    /// 状态码，200表示成功，非200表示错误
    pub code: i32,
    /// 提示信息
    pub message: String,
    /// 返回数据
    pub data: Value,
}

impl ApiResponse {
    /// 成功响应
    pub fn success<T: Serialize>(data: T) -> Self {
        Self::success_with_message(data, "success")
    }

    /// 成功响应，带自定义消息
    pub fn success_with_message<T: Serialize>(data: T, message: impl Into<String>) -> Self {
        Self {
            code: 200,
            message: message.into(),
            data: json!(data),
        }
    }

    /// 成功响应，不带数据
    #[allow(dead_code)]
    pub fn success_without_data() -> Self {
        Self::success_without_data_with_message("success")
    }

    /// 成功响应，不带数据，带自定义消息
    #[allow(dead_code)]
    pub fn success_without_data_with_message(message: impl Into<String>) -> Self {
        Self {
            code: 200,
            message: message.into(),
            data: Value::Null,
        }
    }

    /// 错误响应
    pub fn error(code: i32, message: impl Into<String>) -> Self {
        Self {
            code,
            message: message.into(),
            data: json!({}),
        }
    }

    /// 转换为Json响应
    pub fn to_json(self) -> Json<Self> {
        Json(self)
    }
}
