use serde::{Deserialize, Serialize};

// 新增元件
#[derive(Serialize, Deserialize, Debug)]
pub struct AddComponentParams {
    pub name: String, // 元件名称
    pub keyword: String, // 元件关键字
    pub component_type_id: i32, // 元件所属类型的id
    #[serde(default)]
    pub description: String,  // 元件描述
    #[serde(default)]
    pub remark: Option<String>,
}

// 获取元件列表
#[derive(Serialize, Deserialize, Debug)]
pub struct ListComponentParams {
    pub component_type_id: Option<i32>, // 元件所属类型的id
}

// 定义图的 definition 结构
#[derive(Serialize, Deserialize, Debug)]
pub struct DefinitionParams {
    pub name: String,
    pub keyword: String,
    #[serde(default)]
    pub description: String,
    // 使用 serde_json::Value 捕获所有其他未知字段
    #[serde(flatten)]
    pub other_fields: serde_json::Value,
}

// 保存图定义
#[derive(Serialize, Deserialize, Debug)]
pub struct SaveDiagramParams {
    pub component_id: String,
    pub definition: DefinitionParams, // 使用新的结构体
    pub diagram: serde_json::Value,
    pub property: serde_json::Value,
    #[serde(default)]
    pub create_time: String,
    #[serde(default)]
    pub update_time: String,
}

// 获取图定义
#[derive(Serialize, Deserialize, Debug)]
pub struct GetDiagramParams {
    pub component_id: String,
}
// 删除元件
#[derive(Serialize, Deserialize, Debug)]
pub struct DeleteComponentParams {
    pub component_id: String,
}
// 更新元件
#[derive(Serialize, Deserialize, Debug)]
pub struct UpdateComponentParams {
    pub component_id: String,
    pub name: String,
    pub keyword: String,
    pub component_type_id: i32,
    pub description: Option<String>,
    #[serde(default)]
    pub remark: Option<String>,
}
// 获取单个元件信息
#[derive(Serialize, Deserialize, Debug)]
pub struct GetComponentParams {
    pub component_id: String,
}
// 新增元件类型
#[derive(Serialize, Deserialize, Debug)]
pub struct AddComponentTypeParams {
    pub name: String,
    #[serde(default)]
    pub remark: Option<String>,
}

// 更新元件类型
#[derive(Serialize, Deserialize, Debug)]
pub struct UpdateComponentTypeParams {
    pub id: i32,
    pub name: String,
    #[serde(default)]
    pub remark: Option<String>,
}

// 删除元件类型
#[derive(Serialize, Deserialize, Debug)]
pub struct DeleteComponentTypeParams {
    pub id: i32,
}