use std::sync::Arc;
use axum::Router;
use axum::routing::{get, post};
use crate::api::AppState;
use crate::api::handler::component::{
    add_component, get_component_type_list, list_component, delete_component, update_component,
    get_component, add_component_type, update_component_type, delete_component_type,
};
use crate::api::handler::diagram::{get_diagram, save_diagram};


pub fn api_v1_component() -> Router<Arc<AppState>> {
    Router::new()
        .route("/type/list", get(get_component_type_list))
        .route("/type/add", post(add_component_type))
        .route("/type/update", post(update_component_type))
        .route("/type/delete", post(delete_component_type))
        .route("/add", post(add_component))
        .route("/list", get(list_component))
        .route("/get", get(get_component))
        .route("/update", post(update_component))
        .route("/delete", post(delete_component))
        .route("/diagram/save", post(save_diagram))
        .route("/diagram/get", get(get_diagram))
}