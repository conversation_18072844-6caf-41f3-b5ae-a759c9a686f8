pub mod config;
pub mod data;
pub mod database;
pub mod handler;
pub mod router;
pub mod utils;
pub mod log;

use std::sync::Arc;
use axum::{
    body::Body,
    http::{header, Request, Response},
    Router,
};
use lmdb::{Database, Environment};
use self::database::setup_lmdb_environment;
use self::router::api_v1_component;
use tower::ServiceBuilder;
use tower_http::{
    services::ServeDir,
    cors::{CorsLayer, Any},
};

use axum::middleware::{self, Next};
use tracing::info;
use std::path::PathBuf;

// 应用状态
pub struct AppState {
    pub env: Arc<Environment>,
    pub db: Database,
}

// 定义中间件，并添加 Origin 日志记录
async fn preprocess_middleware(request: Request<Body>, next: Next) -> Response<Body> {
    if let Some(origin) = request.headers().get(header::ORIGIN) {
        info!("Incoming request from Origin: {:?}", origin);
    } else {
        info!("Incoming request with no Origin header.");
    }
    info!("Preprocessing request: {} {}", request.method(), request.uri());
    let response = next.run(request).await;
    info!("Finished processing request.");
    response
}

// 将启动服务器的逻辑提取到一个可配置的函数中
pub async fn start_server(serve_static_files: bool, app_data_dir: PathBuf) -> u16 {
    // 初始化日志
    let _guards = log::init(app_data_dir.clone());

    // 初始化数据库
    info!("Initializing LMDB database...");
    let (env, db) = setup_lmdb_environment(app_data_dir).expect("实例化LMDB数据库失败");
    info!("LMDB database initialized successfully.");
    
    // 创建应用状态
    info!("Creating application state...");
    let state = Arc::new(AppState {
        env: Arc::new(env),
        db: db,
    });
    info!("Application state created successfully.");

    // 暂时恢复为允许所有来源，以便应用可以运行并产生日志
    let cors = CorsLayer::new()
        .allow_origin(Any)
        .allow_methods(Any)
        .allow_headers(Any);

    // 构建API路由并应用中间件
    info!("Building API routes...");
    let v1_router = Router::new().nest("/v1/component", api_v1_component());
    let api_router = Router::new()
        .nest("/api", v1_router)
        .layer(
            ServiceBuilder::new()
                .layer(middleware::from_fn(preprocess_middleware))
        );
    info!("API routes built successfully.");

    // 根据参数决定如何组合路由
    let app = if serve_static_files {
        info!("Serving static files from 'dist' directory.");
        let static_files_service = ServeDir::new("dist")
            .not_found_service(ServeDir::new("dist/index.html"));

        Router::new()
            .nest("/api", api_router.with_state(state.clone()))
            .fallback_service(static_files_service)
            .with_state(state)
            .layer(cors)
    } else {
        // 仅提供API服务
        api_router
            .with_state(state)
            .layer(cors)
    };

    // 固定端口为 7046
    let port = utils::find_available_port(7046).await;
    let addr = format!("0.0.0.0:{}", port);
    let listener = tokio::net::TcpListener::bind(&addr).await.unwrap();
    info!("Axum server listening on {}", addr);
    
    tokio::spawn(async move {
        axum::serve(listener, app).await.unwrap();
    });

    port
}