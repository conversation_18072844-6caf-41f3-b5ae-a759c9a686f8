use serde::Serialize;

#[derive(Debug, Serialize, Default, <PERSON><PERSON>)]
pub struct ComponentType {
    pub id: i32,            // 元件类型id
    pub name: &'static str, // 元件类型名称
}

// 元件类型列表
pub const COMPONENT_TYPE_LIST: [ComponentType; 15] = [
    ComponentType {
        id: 1,
        name: "节点",
    },
    ComponentType {
        id: 2,
        name: "线性无耦合集中参数元件",
    },
    ComponentType {
        id: 3,
        name: "滤波器",
    },
    ComponentType {
        id: 4,
        name: "简单电压源和电流源",
    },
    ComponentType {
        id: 5,
        name: "变压器",
    },
    ComponentType {
        id: 6,
        name: "开关",
    },
    ComponentType {
        id: 7,
        name: "故障",
    },
    ComponentType {
        id: 8,
        name: "HVDC和FACTS元件",
    },
    ComponentType {
        id: 9,
        name: "测量环节",
    },
    ComponentType {
        id: 10,
        name: "线路模型",
    },
    ComponentType {
        id: 11,
        name: "电机模型",
    },
    ComponentType {
        id: 12,
        name: "互阻抗",
    },
    ComponentType {
        id: 13,
        name: "负荷模型",
    },
    ComponentType {
        id: 14,
        name: "电磁暂态文件中混合仿真接口",
    },
    ComponentType {
        id: 15,
        name: "其他",
    },
];

// 定义元件列表在数据库中的主键
pub const COMPONENT_KEY: &str = "components";

// 定义图数据在数据库中的主键
pub const DIAGRAM_KEY: &str = "diagrams";

// 定义自定义元件类型在数据库中的主键
pub const CUSTOM_COMPONENT_TYPE_KEY: &str = "custom_component_types";
