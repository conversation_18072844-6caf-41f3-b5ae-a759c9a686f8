import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "node:path";

const host = process.env.TAURI_DEV_HOST;

// https://vitejs.dev/config/
export default defineConfig(async () => ({
  plugins: [vue()],
  base: "./",
  // 添加路径别名
  resolve: {
    alias: {
      "@/": `${path.resolve(__dirname, "src")}/`,
      "public/": `${path.resolve(__dirname, "public")}/`,
    },
  },

  // Vite options tailored for Tauri development and only applied in `tauri dev` or `tauri build`
  //
  // 1. prevent vite from obscuring rust errors
  clearScreen: false,
  // 2. tauri expects a fixed port, fail if that port is not available
  server: {
    port: 1420,
    strictPort: true,
    host: host || false,
    hmr: host
      ? {
        protocol: "ws",
        host,
        port: 1421,
      }
      : undefined,
    watch: {
      // 3. tell vite to ignore watching `src-tauri`
      ignored: ["**/src-tauri/**"],
    },
    // 4. add proxy to forward /api requests to backend
    proxy: {
      "/api": {
        target: "http://127.0.0.1:7046",
        changeOrigin: true,
      },
    },
  },
}));
